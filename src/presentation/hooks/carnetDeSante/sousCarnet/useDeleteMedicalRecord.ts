import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { action_carnet_de_sante_enum } from "@/domain/models/enums";
import { getTableNameByTitle } from "@/shared/utils/getTableNameByTitle";
import { useAllergie } from "./useAllergie";
import { useMedicament } from "./useMedicament";
import { useAffectationMedicale } from "./useAffectationMedicale";
import { useDispositifMedicaux } from "./useDispositifMedicaux";
import { useAntecedantChirurgicaux } from "./useAntecedantChirurgicaux";
import { useAntecedantFamiliaux } from "./useAntecedantFamiliaux";
import { useAntecedentSociaux } from "./useAntecedentSociaux";
import { useVaccination } from "./useVaccination";
import { useAntecedentGrossesse } from "./useAntecedentGrossesse";
import { useConditionGynecologique } from "./useConditionGynecologique";
import { useDiagnostic } from "./useDiagnostic";
import { useHistoriqueCarnetSante } from "./useHistoriqueCarnetSante";

export const useDeleteMedicalRecord = () => {
  const { remove: removeAllergie } = useAllergie();
  const { remove: removeMedicament } = useMedicament();
  const { remove: removeAffectationMedicale } = useAffectationMedicale();
  const { remove: removeDispositifMedicaux } = useDispositifMedicaux();
  const { remove: removeAntecedantChirurgicaux } = useAntecedantChirurgicaux();
  const { remove: removeAntecedantFamiliaux } = useAntecedantFamiliaux();
  const { remove: removeAntecedentSociaux } = useAntecedentSociaux();
  const { remove: removeVaccination } = useVaccination();
  const { remove: removeAntecedentGrossesse } = useAntecedentGrossesse();
  const { remove: removeConditionGynecologique } = useConditionGynecologique();
  const { remove: removeDiagnostic } = useDiagnostic();
  const { create: createHistoriqueCarnetSante } = useHistoriqueCarnetSante();
  const { idCarnetSante } = useSelector(
    (state: RootState) => state.carnetSante
  );

  const handleDelete = async (
    type: string,
    item: {
      id: number;
      text: string;
    }
  ) => {
    let tableConcernee: string | null = null;
    switch (type) {
      case TITRES_CARNET_DE_SANTE.allergies:
        await removeAllergie(item.id);
        tableConcernee = getTableNameByTitle(TITRES_CARNET_DE_SANTE.allergies);
        break;
      case TITRES_CARNET_DE_SANTE.medicaments:
        await removeMedicament(item.id);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.medicaments
        );
        break;
      case TITRES_CARNET_DE_SANTE.affectationMedicales:
        await removeAffectationMedicale(item.id);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.affectationMedicales
        );
        break;
      case TITRES_CARNET_DE_SANTE.dispositifMedicaux:
        await removeDispositifMedicaux(item.id);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.dispositifMedicaux
        );
        break;
      case TITRES_CARNET_DE_SANTE.antecedantChirurgicaux:
        await removeAntecedantChirurgicaux(item.id);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.antecedantChirurgicaux
        );
        break;
      case TITRES_CARNET_DE_SANTE.antecedantFamiliaux:
        await removeAntecedantFamiliaux(item.id);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.antecedantFamiliaux
        );
        break;
      case TITRES_CARNET_DE_SANTE.antecedentsSociaux:
        await removeAntecedentSociaux(item.id);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.antecedentsSociaux
        );
        break;
      case TITRES_CARNET_DE_SANTE.vaccination:
        await removeVaccination(item.id);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.vaccination
        );
        break;
      case TITRES_CARNET_DE_SANTE.antecedentGrossesse:
        await removeAntecedentGrossesse(item.id);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.antecedentGrossesse
        );
        break;
      case TITRES_CARNET_DE_SANTE.conditionGynecologique:
        await removeConditionGynecologique(item.id);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.conditionGynecologique
        );
        break;
      case TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage:
        await removeDiagnostic(item.id);
        tableConcernee = getTableNameByTitle(
          TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage
        );
        break;
    }
    if (item) {
      createHistoriqueCarnetSante(
        idCarnetSante,
        [{ id: item.id, detail: item.text }],
        tableConcernee,
        action_carnet_de_sante_enum.suppression
      );
    }
  };

  return { handleDelete };
};

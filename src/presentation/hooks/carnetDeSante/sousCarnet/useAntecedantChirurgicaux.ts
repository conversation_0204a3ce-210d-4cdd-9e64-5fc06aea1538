import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { AntecedantChirurgicaux } from '@/domain/models'
import { AppDispatch, RootState } from '@/store'
import {
  createAntecedantChirurgicaux,
  getAntecedantChirurgicaux,
  updateAntecedantChirurgicaux,
  deleteAntecedantChirurgicaux,
  setSelectedAntecedantChirurgicaux,
  setDescriptions,
  setRemarks,
  setDateDeChirurgie,
  resetAntecedantChirurgicauxState,
  clearSelectedAntecedantChirurgicaux
} from '@/application/slices/professionnal/antecedantChirurgicauxSlice'
import { getLocalISOString } from '@/shared/utils/getLocalISOString'

export const useAntecedantChirurgicaux = () => {
  const dispatch = useDispatch<AppDispatch>()
  const {
    antecedantChirurgicaux,
    selectedAntecedantChirurgicaux,
    antecedantChirurgicauxState,
    loading,
    error
  } = useSelector((state: RootState) => state.antecedantChirurgicaux)

  const create = useCallback(
    async (data: Omit<AntecedantChirurgicaux, "id">[]) => {
      const register = await dispatch(createAntecedantChirurgicaux(data))
      const antecedants = register.payload as AntecedantChirurgicaux[]
      return antecedants.map(antecedant => {
        return {
          id: antecedant.id,
          detail: antecedant.nom
        }
      })
    },
    [dispatch]
  )

  const getAll = useCallback(async (carnetId: number) => {
    await dispatch(getAntecedantChirurgicaux(carnetId))
  }, [dispatch])

  const update = useCallback(
    async (id: number, data: Partial<AntecedantChirurgicaux>) => {
      await dispatch(updateAntecedantChirurgicaux({ id, data }))
    },
    [dispatch]
  )

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteAntecedantChirurgicaux(id))
    },
    [dispatch]
  )

  const select = useCallback(
    (antecedantChirurgicaux: AntecedantChirurgicaux | null) => {
      dispatch(setSelectedAntecedantChirurgicaux(antecedantChirurgicaux))
    },
    [dispatch]
  )

  const handleDescriptionsChange = (item: string, value: string) => {
    dispatch(setDescriptions({item, value}))
  };

  const handleDateDeChirurgieChange = (item: string, value: Date | null) => {
    if (!value) return;
    dispatch(setDateDeChirurgie({item, value: getLocalISOString(value)}));
  };
  
  const handleRemarksChange = (item: string, value: string) => {
    dispatch(setRemarks({item, value}))
  };

  const resetState = () => {
    dispatch(resetAntecedantChirurgicauxState())
  };

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedAntecedantChirurgicaux())
  }, [dispatch])

  return {
    antecedantChirurgicaux,
    selectedAntecedantChirurgicaux,
    antecedantChirurgicauxState,
    loading,
    error,
    create,
    getAll,
    update,
    remove,
    select,
    handleRemarksChange,
    handleDescriptionsChange,
    handleDateDeChirurgieChange,
    resetState,
    clearSelected
  }
}

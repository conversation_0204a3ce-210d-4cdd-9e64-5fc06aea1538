import { useEffect, useState } from "react";
import { useCarnetDeSante } from "./useCarnetDeSante";
import { useAllergie } from "@/presentation/hooks/carnetDeSante/sousCarnet/useAllergie";
import { useMedicament } from "@/presentation/hooks/carnetDeSante/sousCarnet/useMedicament";
import { useAffectationMedicale } from "@/presentation/hooks/carnetDeSante/sousCarnet/useAffectationMedicale";
import { useDispositifMedicaux } from "@/presentation/hooks/carnetDeSante/sousCarnet/useDispositifMedicaux";
import { useAntecedantChirurgicaux } from "@/presentation/hooks/carnetDeSante/sousCarnet/useAntecedantChirurgicaux";
import { useAntecedantFamiliaux } from "@/presentation/hooks/carnetDeSante/sousCarnet/useAntecedantFamiliaux";
import { useAntecedentSociaux } from "@/presentation/hooks/carnetDeSante/sousCarnet/useAntecedentSociaux";
import { useVaccination } from "@/presentation/hooks/carnetDeSante/sousCarnet/useVaccination";
import { CarnetSante } from "@/domain/models";
import { CarnetSanteDTO } from "@/domain/DTOS";
import { useConditionGynecologique } from "@/presentation/hooks/carnetDeSante/sousCarnet/useConditionGynecologique";
import { useAntecedentGrossesse } from "@/presentation/hooks/carnetDeSante/sousCarnet/useAntecedentGrossesse";
import { useSigneVitaux } from "@/presentation/hooks/signeVitaux";
import { useMedicalConsultation } from "@/presentation/hooks/consultationMedicale";
import { useDiagnostic } from "@/presentation/hooks/carnetDeSante/sousCarnet/useDiagnostic";
import { useFacturation } from "@/presentation/hooks/facturation";

export const useCarnetDeSanteData = (patientId?: number) => {
  const {
    idCarnetSante,
    loading,
    getId: getIdCarnetSantes,
    create: createCarnetSante,
  } = useCarnetDeSante();
  const { allergies, getAll: getAllergies } = useAllergie();
  const { medicaments, getAll: getMedicaments } = useMedicament();
  const { affectationMedicales, getAll: getAllAffectationMedicales } =
    useAffectationMedicale();
  const { dispositifMedicaux, getAll: getAllDispositifMedicaux } =
    useDispositifMedicaux();
  const { antecedantChirurgicaux, getAll: getAllAntecedantChirurgicaux } =
    useAntecedantChirurgicaux();
  const { antecedantFamiliaux, getAll: getAllAntecedantFamiliaux } =
    useAntecedantFamiliaux();
  const { antecedentSociaux, getAll: getAllAntecedentSociaux } =
    useAntecedentSociaux();
  const { vaccinations, getAll: getAllVaccinations } = useVaccination();
  const { conditionGynecologique, getAll: getAllConditionGynecologique } =
    useConditionGynecologique();
  const { antecedentGrossesse, getAll: getAllAntecedentGrossesse } =
    useAntecedentGrossesse();
  const { signeVitaux, getAll: fetchSigneVitaux } = useSigneVitaux();
  const { diagnostics, getAll: getAllDiagnostics } = useDiagnostic();
  const { consultations, getMedicalConsultationsByPatientId } =
    useMedicalConsultation();
  const { listeFacturationPatient, getFacturationsByPatientId } =
    useFacturation();

  const [isSubmite, seIsSubmite] = useState(false);

  useEffect(() => {
    if (patientId) {
      getIdCarnetSantes(patientId);
      getFacturationsByPatientId(patientId);
    }
  }, [patientId, isSubmite]);

  useEffect(() => {
    if (idCarnetSante) {
      getAllergies(idCarnetSante);
      getMedicaments(idCarnetSante);
      getAllAffectationMedicales(idCarnetSante);
      getAllDispositifMedicaux(idCarnetSante);
      getAllAntecedantChirurgicaux(idCarnetSante);
      getAllAntecedantFamiliaux(idCarnetSante);
      getAllAntecedentSociaux(idCarnetSante);
      getAllVaccinations(idCarnetSante);
      getAllConditionGynecologique(idCarnetSante);
      getAllAntecedentGrossesse(idCarnetSante);
      getAllDiagnostics(idCarnetSante);
      fetchSigneVitaux(idCarnetSante);
      getMedicalConsultationsByPatientId(idCarnetSante);
      // ... charger autres données
    }
  }, [idCarnetSante]);

  const handleSubmit = async (data: Omit<CarnetSante, "id">) => {
    await createCarnetSante(data);
    seIsSubmite(true);
  };

  return {
    idCarnetSante: idCarnetSante,
    loading,
    data: {
      allergie: allergies,
      medicament: medicaments,
      affectation_medical: affectationMedicales,
      dispositif_medicaux: dispositifMedicaux,
      antecedant_chirurgicaux: antecedantChirurgicaux,
      antecedant_familliaux: antecedantFamiliaux,
      antecedant_sociaux: antecedentSociaux,
      vaccination: vaccinations,
      condition_gynecologique: conditionGynecologique,
      antecedent_grossesse: antecedentGrossesse,
      diagnostic: diagnostics,
      // ...
    } as Partial<CarnetSanteDTO>,
    signeVitaux: signeVitaux,
    consultations: consultations,
    facturations: listeFacturationPatient,
    handleSubmit,
  };
};

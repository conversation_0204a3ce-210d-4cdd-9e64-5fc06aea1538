import { CarnetSante, Patient } from "@/domain/models";
import { AppDispatch, RootState } from "@/store";
import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  createCarnetSante,
  getCarnetSantes,
  getIdCarnetSante,
  updateCarnetSante,
  deleteCarnetSante,
  setSelectedCarnetSante,
  setSelectedSearch,
  onDeleteSelectedSearch,
  resetSearchState,
  resetId,
  clearSelectedCarnetSante,
} from "@/application/slices/professionnal/carnetSanteSlice";
import {
  decedePatient,
  updatePatient,
} from "@/application/slices/auth/authSlice";

export const useCarnetDeSante = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    idCarnetSante,
    carnetSantes,
    selectedSearch,
    selectedCarnetSante,
    loading,
    error,
  } = useSelector((state: RootState) => state.carnetSante);

  const create = useCallback(
    async (data: Omit<CarnetSante, "id">) => {
      await dispatch(createCarnetSante(data));
    },
    [dispatch]
  );

  const get = useCallback(
    async (id: number) => {
      await dispatch(getCarnetSantes(id));
    },
    [dispatch]
  );

  const getId = useCallback(
    async (id: number) => {
      dispatch(resetId());
      await dispatch(getIdCarnetSante(id));
    },
    [dispatch]
  );

  const update = useCallback(
    async (id: number, data: Partial<CarnetSante>) => {
      await dispatch(updateCarnetSante({ id, data }));
    },
    [dispatch]
  );

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteCarnetSante(id));
    },
    [dispatch]
  );

  const select = useCallback(
    (carnetSante: CarnetSante | null) => {
      dispatch(setSelectedCarnetSante(carnetSante));
    },
    [dispatch]
  );

  const handleDecedePatient = (id: number) => {
    dispatch(decedePatient(id));
  };

  const handleUpdatePatient = (id: number, data: Partial<Patient>) => {
    dispatch(updatePatient({ id, data }));
  };

  const handleDeleteSelected = (value: string) => {
    dispatch(onDeleteSelectedSearch({ itemToDelete: value }));
  };

  const handleSearchChange = (value: string) => {
    dispatch(setSelectedSearch({ value }));
  };

  const resetSearch = () => {
    dispatch(resetSearchState());
  };

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedCarnetSante());
  }, [dispatch]);

  return {
    idCarnetSante,
    carnetSantes,
    selectedSearch,
    selectedCarnetSante,
    loading,
    error,
    create,
    get,
    getId,
    update,
    remove,
    select,
    handleSearchChange,
    handleDeleteSelected,
    handleDecedePatient,
    handleUpdatePatient,
    resetSearch,
    clearSelected,
  };
};

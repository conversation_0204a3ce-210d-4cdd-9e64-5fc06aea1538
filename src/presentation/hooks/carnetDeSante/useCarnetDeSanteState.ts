import { useCarnetDeSanteStateContext } from "@/presentation/contexts/useContext/useCarnetDeSanteStateContext";
import { active_tab_enum } from "@/domain/models/enums";

export const useCarnetDeSanteState = () => {
  const { state, dispatch } = useCarnetDeSanteStateContext();

  const updateField = (
    field: string,
    value: active_tab_enum | boolean | File
  ) => {
    dispatch({
      type: "UPDATE_FIELD",
      payload: { field, value },
    });
  };

  const handleActiveTabChange = (value: active_tab_enum) => {
    updateField("activeTab", value);
  };

  const setIsProfile = (value: boolean) => {
    updateField("isProfile", value);
  };

  const setIsAddForm = (value: boolean) => {
    updateField("isAddForm", value);
  };

  const setSelectedFile = (value: File | null) => {
    updateField("selectedFile", value);
  };

  const resetState = () => {
    dispatch({
      type: "RESET_STATE",
    });
  };

  return {
    ...state,
    handleActiveTabChange,
    setIsProfile,
    setIsAddForm,
    setSelectedFile,
    resetSelectedFileState: resetState,
  };
};

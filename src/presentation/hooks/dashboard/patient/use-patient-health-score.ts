import { useState, useEffect } from 'react';
import { signe_vitaux } from '@/domain/models';

export interface HealthScoreData {
  score: number;
  status: 'Excellente' | 'Bonne' | 'Moyenne' | 'À surveiller' | 'Préoccupante';
  trend: {
    value: number;
    isPositive: boolean;
    label: string;
  };
  factors: {
    vitals: number;
    appointments: number;
    prescriptions: number;
    lifestyle: number;
  };
}

interface HealthScoreParams {
  signeVitaux?: signe_vitaux[];
  appointmentHistory?: any[];
  prescriptions?: any[];
  allergies?: any[];
  chronicConditions?: any[];
  patientAge?: number;
}

export const usePatientHealthScore = (params: HealthScoreParams) => {
  const [healthScore, setHealthScore] = useState<HealthScoreData>({
    score: 0,
    status: 'Moyenne',
    trend: { value: 0, isPositive: true, label: 'stable' },
    factors: { vitals: 0, appointments: 0, prescriptions: 0, lifestyle: 0 }
  });

  const calculateVitalScore = (vitals: signe_vitaux[]): number => {
    if (!vitals || vitals.length === 0) return 50; // Score neutre si pas de données

    const latest = vitals[vitals.length - 1];
    let score = 100;

    // IMC (Indice de Masse Corporelle)
    if (latest.indice_masse_corporel) {
      const imc = latest.indice_masse_corporel;
      if (imc < 18.5 || imc > 30) score -= 15;
      else if (imc < 20 || imc > 25) score -= 5;
    }

    // Tension artérielle (si disponible)
    if (latest.tension_arterielle) {
      const tension = latest.tension_arterielle.split('/');
      const systolique = parseInt(tension[0]);
      const diastolique = parseInt(tension[1]);
      
      if (systolique > 140 || diastolique > 90) score -= 20;
      else if (systolique > 130 || diastolique > 85) score -= 10;
    }

    // Fréquence cardiaque
    if (latest.frequence_cardiaque) {
      const fc = latest.frequence_cardiaque;
      if (fc < 60 || fc > 100) score -= 10;
    }

    // Température
    if (latest.temperature) {
      const temp = latest.temperature;
      if (temp < 36 || temp > 37.5) score -= 15;
    }

    // Glucose
    if (latest.niveau_glucose) {
      const glucose = latest.niveau_glucose;
      if (glucose > 126) score -= 20; // Diabète
      else if (glucose > 100) score -= 10; // Pré-diabète
    }

    return Math.max(0, Math.min(100, score));
  };

  const calculateAppointmentScore = (appointments: any[]): number => {
    if (!appointments || appointments.length === 0) return 30;

    const now = new Date();
    const sixMonthsAgo = new Date(now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000);
    
    const recentAppointments = appointments.filter(apt => 
      new Date(apt.date) >= sixMonthsAgo
    );

    // Score basé sur la régularité des visites
    if (recentAppointments.length >= 3) return 100;
    if (recentAppointments.length >= 2) return 80;
    if (recentAppointments.length >= 1) return 60;
    return 30;
  };

  const calculatePrescriptionScore = (prescriptions: any[]): number => {
    if (!prescriptions || prescriptions.length === 0) return 100;

    const activePrescriptions = prescriptions.filter(p => p.isActive);
    const expiredPrescriptions = prescriptions.filter(p => 
      !p.isActive && new Date(p.endDate) < new Date()
    );

    let score = 100;

    // Pénalité pour trop de médicaments actifs
    if (activePrescriptions.length > 5) score -= 20;
    else if (activePrescriptions.length > 3) score -= 10;

    // Pénalité pour médicaments expirés non renouvelés
    if (expiredPrescriptions.length > 2) score -= 15;

    return Math.max(0, score);
  };

  const calculateLifestyleScore = (allergies: any[], chronicConditions: any[], age: number): number => {
    let score = 100;

    // Pénalité pour allergies multiples
    if (allergies && allergies.length > 3) score -= 15;
    else if (allergies && allergies.length > 1) score -= 5;

    // Pénalité pour conditions chroniques
    if (chronicConditions && chronicConditions.length > 2) score -= 25;
    else if (chronicConditions && chronicConditions.length > 0) score -= 10;

    // Ajustement selon l'âge
    if (age > 65) score -= 5; // Risque naturellement plus élevé
    if (age < 18) score += 5; // Généralement en meilleure santé

    return Math.max(0, score);
  };

  const getHealthStatus = (score: number): HealthScoreData['status'] => {
    if (score >= 90) return 'Excellente';
    if (score >= 75) return 'Bonne';
    if (score >= 60) return 'Moyenne';
    if (score >= 40) return 'À surveiller';
    return 'Préoccupante';
  };

  const calculateTrend = (currentVitals: signe_vitaux[], previousVitals: signe_vitaux[]): HealthScoreData['trend'] => {
    if (!currentVitals || !previousVitals || currentVitals.length < 2) {
      return { value: 0, isPositive: true, label: 'stable' };
    }

    const currentScore = calculateVitalScore([currentVitals[currentVitals.length - 1]]);
    const previousScore = calculateVitalScore([currentVitals[currentVitals.length - 2]]);
    
    const difference = currentScore - previousScore;
    
    if (Math.abs(difference) < 5) {
      return { value: 0, isPositive: true, label: 'stable' };
    }
    
    return {
      value: Math.abs(difference),
      isPositive: difference > 0,
      label: difference > 0 ? 'amélioration' : 'dégradation'
    };
  };

  useEffect(() => {
    const {
      signeVitaux = [],
      appointmentHistory = [],
      prescriptions = [],
      allergies = [],
      chronicConditions = [],
      patientAge = 30
    } = params;

    // Calcul des scores par catégorie
    const vitalScore = calculateVitalScore(signeVitaux);
    const appointmentScore = calculateAppointmentScore(appointmentHistory);
    const prescriptionScore = calculatePrescriptionScore(prescriptions);
    const lifestyleScore = calculateLifestyleScore(allergies, chronicConditions, patientAge);

    // Score global pondéré
    const globalScore = Math.round(
      (vitalScore * 0.4) +           // 40% - Signes vitaux
      (appointmentScore * 0.25) +    // 25% - Suivi médical
      (prescriptionScore * 0.20) +   // 20% - Gestion médicaments
      (lifestyleScore * 0.15)        // 15% - Facteurs de style de vie
    );

    const trend = calculateTrend(signeVitaux, signeVitaux);

    setHealthScore({
      score: globalScore,
      status: getHealthStatus(globalScore),
      trend,
      factors: {
        vitals: vitalScore,
        appointments: appointmentScore,
        prescriptions: prescriptionScore,
        lifestyle: lifestyleScore
      }
    });
  }, [params]);

  return healthScore;
};

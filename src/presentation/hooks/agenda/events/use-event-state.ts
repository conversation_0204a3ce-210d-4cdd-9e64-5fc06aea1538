import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  setTitle,
  setIsAllDay,
  setDescription,
  setIsReported,
  setSelectedDateDebut,
  setSelectedDateFin,
  setStartTime,
  setEndTime,
  setRepetition,
  resetState,
} from "@/application/slices/statesInComponent/addEventStateSlice";
import { Evenement } from "@/domain/models";
import { getLocalISOString } from "@/shared/utils/getLocalISOString";

export const useEventState = () => {
  const dispatch = useDispatch();
  const {
    title,
    est_toute_la_journee,
    description,
    est_reporte,
    date_debut,
    date_fin,
    heure_debut,
    heure_fin,
    repetition,
  } = useSelector((state: RootState) => state.addEventState);

  const handleTitleChange = (value: string) => {
    dispatch(setTitle(value));
  };

  const handleIsAllDayChange = (value: boolean) => {
    dispatch(setIsAllDay(value));
  };

  const handleDescriptionChange = (value: string) => {
    dispatch(setDescription(value));
  };

  const handleIsReportedChange = (value: boolean) => {
    dispatch(setIsReported(value));
  };

  const handleDateDebutChange = (value: Date | null) => {
    if (!value) return;
    const dateDebut = new Date(value);
    const [heureDebut, minutesDebut, _] = date_debut.split("T")[1].split(":");
    dateDebut.setHours(parseInt(heureDebut), parseInt(minutesDebut), 0, 0);
    dispatch(setSelectedDateDebut(getLocalISOString(dateDebut)));
    if (!est_toute_la_journee) {
      // Si l'évenement n'est pas toute la journée, on met à jour la date de fin et son heure setHours (startTime, endTime)
      const dateFin = new Date(value);
      dateFin.setHours(value.getHours() + 1, 0, 0, 0);
      const [heureFin, minutesFin, _] = date_fin.split("T")[1].split(":");
      dateFin.setHours(parseInt(heureFin), parseInt(minutesFin), 0, 0);
      dispatch(setSelectedDateFin(getLocalISOString(dateFin)));
    }
  };

  const handleDateFinChange = (value: Date | null) => {
    if (!value) return;
    dispatch(setSelectedDateFin(getLocalISOString(value)));
  };

  const handleStartTimeChange = (value: string) => {
    const [hours, minutes] = value.split(":").map(Number);
    const dateObj = new Date(date_debut);
    dateObj.setHours(hours, minutes, 0, 0);
    dispatch(setStartTime(value));
    dispatch(setSelectedDateDebut(getLocalISOString(dateObj)));
  };

  const handleEndTimeChange = (value: string) => {
    const [hours, minutes] = value.split(":").map(Number);
    const dateObj = new Date(date_fin);
    dateObj.setHours(hours, minutes, 0, 0);
    dispatch(setEndTime(value));
    dispatch(setSelectedDateFin(getLocalISOString(dateObj)));
  };

  const handleRepetitionChange = (value: string) => {
    dispatch(setRepetition(value));
  };

  const initializeState = (evenement: Evenement) => {
    const dateDebut = new Date(evenement.date_debut);
    const dateFin = new Date(evenement.date_fin);

    dispatch(setTitle(evenement.titre));
    dispatch(setIsAllDay(evenement.est_toute_la_journee));
    dispatch(setDescription(evenement.description));
    dispatch(setIsReported(evenement.est_reportee));
    dispatch(setSelectedDateDebut(getLocalISOString(dateDebut)));
    dispatch(setSelectedDateFin(getLocalISOString(dateFin)));
    dispatch(
      setStartTime(
        `${dateDebut.getHours().toString().padStart(2, "0")}:${dateDebut.getMinutes().toString().padStart(2, "0")}`
      )
    );
    dispatch(
      setEndTime(
        `${dateFin.getHours().toString().padStart(2, "0")}:${dateFin.getMinutes().toString().padStart(2, "0")}`
      )
    );
    dispatch(setRepetition(evenement.repetition));
  };

  return {
    title,
    est_toute_la_journee,
    description,
    est_reporte,
    date_debut,
    date_fin,
    heure_debut,
    heure_fin,
    repetition,
    handleTitleChange,
    handleIsAllDayChange,
    handleDescriptionChange,
    handleIsReportedChange,
    handleDateDebutChange,
    handleDateFinChange,
    handleStartTimeChange,
    handleEndTimeChange,
    handleRepetitionChange,
    initializeState,
    resetState: () => dispatch(resetState()),
  };
};

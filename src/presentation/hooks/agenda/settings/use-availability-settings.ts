import { horaire_date_specifique, horaire_hebdomadaire, parametre_disponibilite } from '@/domain/models'
import {
  deleteSettings,
  fetchSettings,
  resetSettings,
  saveSettings,
  updateSettings,
  deleteThisSettings,
  deleteWeeklySettings,
} from '@/application/slices/professionnal/availabilitySettingsSlice'
import { useDispatch, useSelector } from 'react-redux'
import { AppDispatch, RootState } from '@/store'
import { AvailabilitySettingsDTO } from '@/domain/DTOS'

export const useAvailabilitySettings = () => {
  const dispatch: AppDispatch = useDispatch()
  const { settings, loading, error } = useSelector(
    (state: RootState) => state.availabilitySettings
  )

  const handleSaveSettings = async (settingsData: Omit<AvailabilitySettingsDTO, 'id'>) => {
    await dispatch(saveSettings(settingsData)).unwrap()
  }

  const handleUpdateSettings = async (
    id: number,
    settingsData: Partial<parametre_disponibilite>
  ) => {
    await dispatch(updateSettings({ id, settings: settingsData })).unwrap()
  }

  const handleFetchSettings = async (professionalId: number) => {
    if (!navigator.onLine) {
      throw new Error(
        'Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.'
      )
    }
    await dispatch(fetchSettings(professionalId)).unwrap()
  }

  const handleDeleteSettings = async (professionalId: number) => {
    if (!navigator.onLine) {
      throw new Error(
        'Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.'
      )
    }
    await dispatch(deleteSettings(professionalId)).unwrap()
  }

  const handleDeleteThis = async (exceptions: horaire_date_specifique[], id_parametre_disponibilite: number) => {
    if (!navigator.onLine) {
      throw new Error(
        'Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.'
      )
    }
    await dispatch(deleteThisSettings({ exceptions, id_parametre_disponibilite })).unwrap()
  }

  const handleDeleteWeeklySettings = async (data: horaire_hebdomadaire[], id_parametre_disponibilite: number) => {
    if (!navigator.onLine) {
      throw new Error(
        'Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.'
      )
    }
    await dispatch(deleteWeeklySettings({ data, id_parametre_disponibilite })).unwrap()
  }

  const handleResetSettings = () => {
    dispatch(resetSettings())
  }

  return {
    settings,
    loading,
    error,
    saveSettings: handleSaveSettings,
    updateSettings: handleUpdateSettings,
    getSettings: handleFetchSettings,
    deleteSettings: handleDeleteSettings,
    deleteThis: handleDeleteThis,
    deleteWeeklySettings: handleDeleteWeeklySettings,
    resetSettings: handleResetSettings,
  }
}

export default useAvailabilitySettings

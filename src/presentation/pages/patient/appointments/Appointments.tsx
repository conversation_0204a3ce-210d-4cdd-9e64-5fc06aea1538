import { useState, useMemo, useEffect } from "react";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import AppointmentFilters from "@/presentation/components/common/appointment/AppointmentFilters";
import { Typography } from "@mui/material";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { useAppSelector } from "@/presentation/hooks/redux";
import { rendez_vous_statut_enum } from "@/domain/models/enums";

const Appointments = () => {
  const { appointmentPatient, fetchAppointmentListByPatientId } =
    useConsultationState();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string>(
    rendez_vous_statut_enum.A_VENIR
  );
  const patientId = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const role = useAppSelector((state) => state.authentification.user?.role);

  const filteredAppointments = useMemo(() => {
    return appointmentPatient.filter((appointment) => {
      const matchesSearch =
        appointment.professional.nom
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        appointment.professional.prenom
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        appointment.motif.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesStatus =
        selectedStatus === "all" || appointment.statut === selectedStatus;

      return matchesSearch && matchesStatus;
    });
  }, [appointmentPatient, searchQuery, selectedStatus]);

  useEffect(() => {
    if (patientId) {
      fetchAppointmentListByPatientId(patientId);
    }
  }, [patientId]);

  return (
    <div>
      <div className="mb-2">
        <Typography variant="h5" component="h1" gutterBottom>
          Rendez-vous
        </Typography>
        <Typography variant="subtitle1" color="textSecondary">
          Total: {filteredAppointments.length} rendez-vous
        </Typography>
      </div>

      <div>
        <AppointmentFilters
          searchQuery={searchQuery}
          selectedStatus={selectedStatus}
          onSearchChange={setSearchQuery}
          onStatusChange={setSelectedStatus}
        />

        <ListDataGrid data={filteredAppointments} type={role} />
      </div>
    </div>
  );
};

export default Appointments;

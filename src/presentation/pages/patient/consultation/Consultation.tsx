import { useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  styled,
  But<PERSON>,
  IconButton,
} from "@mui/material";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import UnathenticatedLayout from "@/presentation/components/layouts/UnauthenticatedLayout";
import ConsultationStep1 from "@/presentation/components/features/patient/consultationStepper/ConsultationStep1/ConsultationStep1";
import ConsultationStep2 from "@/presentation/components/features/patient/consultationStepper/ConsultationStep2/ConsultationStep2";
import ConsultationStep3 from "@/presentation/components/features/patient/consultationStepper/ConsultationStep3/ConsultationStep3";
import ConsultationStep4 from "@/presentation/components/features/patient/consultationStepper/ConsultationStep4/ConsultationStep4";
import ConsultationStep5 from "@/presentation/components/features/patient/consultationStepper/ConsultationStep5/ConsultationStep5";
import { EmailValidator } from "@/domain/services/EmailValidator";
import PhoneNumberValidator from "@/domain/services/PhoneNumberValidator";
import { Patient, <PERSON>che } from "@/domain/models";
import { useLocation } from "react-router-dom";
import useProfessionals from "@/presentation/hooks/use-professionals";

const steps = [
  "Choix du motif",
  "Identification",
  "Vérification",
  "Infos patient",
  "Confirmation",
];

const StyledBox = styled(Box)(({ theme }) => ({
  width: "100%",
  padding: theme.spacing(3),
}));

const Consultation = () => {
  const { loading, getProfessionalsById } = useProfessionals();
  const {
    activeStep,
    isPageValid,
    speciality,
    consultationType,
    consultationMotif,
    phone,
    email,
    confirmEmail,
    password,
    acceptConditions,
    forWhom,
    procheInfo,
    userInformations,
    handleIsPageValidChange,
    handleActiveStepChange,
    handleresetState,
  } = useConsultationState();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const id = searchParams.get("id");

  useEffect(() => {
    const validateCurrentStep = () => {
      const emailValidator = new EmailValidator();
      const phoneNumberValidator = new PhoneNumberValidator();

      switch (activeStep) {
        case 0:
          handleIsPageValidChange(
            speciality !== "" && consultationMotif !== ""
          );
          break;
        case 1:
          emailValidator.isValid(email);
          phoneNumberValidator.isValid(email);

          handleIsPageValidChange(
            phoneNumberValidator.isValid(phone) &&
              emailValidator.isValid(email) &&
              email === confirmEmail &&
              password.length >= 8 &&
              acceptConditions
          );
          break;
        case 2:
          handleIsPageValidChange(false);
          break;
        case 3:
          if (forWhom === "self") {
            handleIsPageValidChange(validateInfoStep4(userInformations));
          } else {
            handleIsPageValidChange(
              validateInfoStep4(userInformations) &&
                validateInfoStep4(procheInfo)
            );
          }
          break;
        default:
          handleIsPageValidChange(false);
      }
    };

    validateCurrentStep();
  }, [
    activeStep,
    speciality,
    consultationType,
    consultationMotif,
    phone,
    email,
    confirmEmail,
    password,
    acceptConditions,
    forWhom,
    procheInfo,
    userInformations,
    handleIsPageValidChange,
    handleActiveStepChange,
  ]);

  const validateInfoStep4 = (info: Proche | Patient | null) => {
    if (!info) return false;

    return (
      info.sexe &&
      info.nom.trim() !== "" &&
      info.prenom.trim() !== "" &&
      info.date_naissance !== null
    );
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return <ConsultationStep1 />;
      case 1:
        return <ConsultationStep2 />;
      case 2:
        return <ConsultationStep3 />;
      case 3:
        return <ConsultationStep4 />;
      case 4:
        return <ConsultationStep5 />;
      default:
        return null;
    }
  };

  useEffect(() => {
    if (isPageValid && activeStep === 0) {
      const timeout = setTimeout(() => {
        if (activeStep < steps.length) {
          handleActiveStepChange(activeStep + 1);
        }
      }, 1000);
      return () => clearTimeout(timeout);
    }
  }, [isPageValid, activeStep, handleActiveStepChange]);

  useEffect(() => {
    if (id) {
      getProfessionalsById(Number(id));
    }
    handleresetState();
  }, [id]);

  return (
    <UnathenticatedLayout>
      <StyledBox>
        <Stepper activeStep={activeStep} alternativeLabel>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel sx={{ "& .MuiStepLabel-label": { fontWeight: 600 } }}>
                {label}
              </StepLabel>
            </Step>
          ))}
        </Stepper>
        <Box sx={{ mt: 4 }}>{renderStepContent()}</Box>
      </StyledBox>
    </UnathenticatedLayout>
  );
};

export default Consultation;

import { Avatar, Box, Paper, Typography } from "@mui/material";
import {
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Event as EventIcon,
  Healing as HealingIcon,
  List as ListIcon,
} from "@mui/icons-material";
import { useRef } from "react";
import { WarningBox, WarningBox2 } from "@/styles/styleMui/ConsultationSteper";
import useProfessionals from "@/presentation/hooks/use-professionals";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { useLocation } from "react-router-dom";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";

interface SumUpProps {
  children?: React.ReactNode;
}

const SumUp: React.FC<SumUpProps> = ({ children }) => {
  const stickyResumeRef = useRef<HTMLDivElement>(null);
  const { selectedProfessionnal } = useProfessionals();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const date = searchParams.get("date");
  const { speciality, consultationMotif } = useConsultationState();
  return (
    <div className="lg:col-span-1">
      <Paper
        className="p-4 sticky top-[100px] border border-gray-200 shadow-md rounded-lg"
        ref={stickyResumeRef}
      >
        <Box className="mY-1">
          <Typography
            variant="h6"
            color="green"
            align="center"
            gutterBottom
            sx={{ fontWeight: "semi-bold" }}
          >
            Récapulatif
          </Typography>
        </Box>
        <Box className="mY-1">
          <Avatar
            sx={{ width: 70, height: 70, marginX: "auto", marginBottom: 1 }}
          />
        </Box>
        <Box className="mY-1">
          <Typography variant="h6" align="center" gutterBottom>
            {selectedProfessionnal?.nom} {selectedProfessionnal?.prenom}
          </Typography>
          {speciality && (
            <Typography
              variant="body2"
              color="textSecondary"
              align="center"
              gutterBottom
            >
              {speciality}
            </Typography>
          )}
        </Box>
        <Box className="mY-1">{children}</Box>
        <Box className="mY-1">
          <Box display="flex" alignItems="flex-start" gap={1} mb={1}>
            <EventIcon />
            <Typography variant="body2" color="textSecondary" gutterBottom>
              {format(new Date(date), "EEEE dd MMMM yyyy", {
                locale: fr,
              })}
            </Typography>
          </Box>
          {consultationMotif && (
            <Box display="flex" alignItems="flex-start" gap={1} mb={1}>
              <HealingIcon />
              <Typography variant="body2" color="textSecondary" gutterBottom>
                {consultationMotif}
              </Typography>
            </Box>
          )}
          <Box display="flex" alignItems="flex-start" gap={1} mb={1}>
            <LocationIcon />
            <Typography variant="body2" color="textSecondary" gutterBottom>
              {selectedProfessionnal?.adresse}
            </Typography>
          </Box>
        </Box>
      </Paper>
    </div>
  );
};

export default SumUp;

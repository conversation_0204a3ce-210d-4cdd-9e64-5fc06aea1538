import { FC, useState, useCallback } from "react";
import { useAppSelector } from "@/presentation/hooks/redux";
import {
  Calendar,
  TrendingUp,
  Users,
  Clock,
  Activity,
  CheckCircle,
  AlertCircle,
  BarChart2,
  <PERSON><PERSON><PERSON>,
  Heart,
  FileText,
} from "lucide-react";
import {
  XAxis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Bar,
  BarChart,
  LineChart,
  Line,
} from "recharts";

// Composants
import StatCard from "@/presentation/components/common/StatCard";
import NextAppointment from "@/presentation/components/features/patient/appointments/NextAppointment";
import ActivePrescriptions from "@/presentation/components/features/patient/prescriptions/ActivePrescriptions";
import PatientDistributionChart from "@/presentation/components/features/professional/appointment/stats/PatientDistributionChart";

// Types et hooks
import {
  PatientAppointment,
  Prescription,
} from "@/presentation/types/patient.types";
import { useDarkMode } from "@/presentation/hooks/use-dark-mode";

const Dashboard: FC = () => {
  const isDarkMode = useDarkMode();
  const patientId = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const patientName = useAppSelector(
    (state) => state.authentification.userData?.nom
  );

  // Ces données viendraient normalement d'une API
  const [nextAppointment] = useState<PatientAppointment>({
    id: "1",
    doctorName: "Dr. Marie Dubois",
    specialty: "Médecin Généraliste",
    date: "2025-03-15",
    time: "14:30",
    duration: 30,
    status: "upcoming",
    location: "123 Avenue de la Santé, 75001 Paris",
  });

  const [prescriptions] = useState<Prescription[]>([
    {
      id: "1",
      medication: "Amoxicilline",
      dosage: "500mg",
      frequency: "3x par jour",
      startDate: "25 Février 2025",
      endDate: "3 Mars 2025",
      doctorName: "Marie Dubois",
      isActive: true,
      refillsLeft: 2,
    },
  ]);

  // Données mockées pour les statistiques
  const [appointmentHistory] = useState([
    { name: "Jan", appointments: 2 },
    { name: "Fév", appointments: 1 },
    { name: "Mar", appointments: 3 },
    { name: "Avr", appointments: 2 },
    { name: "Mai", appointments: 1 },
    { name: "Juin", appointments: 2 },
  ]);

  const [healthMetrics] = useState([
    { name: "Jan", poids: 70, tension: 120 },
    { name: "Fév", poids: 69, tension: 118 },
    { name: "Mar", poids: 68, tension: 115 },
    { name: "Avr", poids: 68, tension: 117 },
    { name: "Mai", poids: 67, tension: 116 },
    { name: "Juin", poids: 67, tension: 114 },
  ]);

  const [prescriptionStatus] = useState([
    { name: "Actives", value: 3, color: "#4F46E5" },
    { name: "Terminées", value: 7, color: "#10B981" },
    { name: "Expirées", value: 1, color: "#F59E0B" },
  ]);

  // Définir les styles du tooltip en fonction du mode
  const tooltipContentStyle = isDarkMode
    ? {
        backgroundColor: "rgba(31, 41, 55, 0.9)",
        borderColor: "#374151",
        color: "#f9fafb",
      }
    : {
        backgroundColor: "rgba(255, 255, 255, 0.9)",
        borderColor: "#e5e7eb",
        color: "#111827",
      };

  const tooltipItemStyle = isDarkMode
    ? { color: "#f3f4f6" }
    : { color: "#111827" };

  const tooltipLabelStyle = isDarkMode
    ? { color: "#f3f4f6" }
    : { color: "#111827" };

  const handleCancelAppointment = useCallback((id: string) => {
    // Implémenter la logique d'annulation
    console.log("Annulation du rendez-vous:", id);
  }, []);

  const handleRequestRefill = useCallback((id: string) => {
    // Implémenter la logique de renouvellement
    console.log("Demande de renouvellement:", id);
  }, []);

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* En-tête avec salutation */}
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Bonjour, {patientName || "Patient"}
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Voici un aperçu de votre suivi médical
        </p>
      </header>

      {/* Cartes de statistiques principales */}
      <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Rendez-vous Total"
          value={11}
          icon={
            <Calendar className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
          }
          trend={null}
          bgColor="bg-indigo-50 dark:bg-indigo-900/30"
        />
        <StatCard
          title="Prochains RDV"
          value={2}
          icon={
            <Clock className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
          }
          trend={{
            value: 1,
            isPositive: true,
            label: "ce mois-ci",
          }}
          bgColor="bg-emerald-50 dark:bg-emerald-900/30"
        />
        <StatCard
          title="Ordonnances Actives"
          value={prescriptions.filter((p) => p.isActive).length}
          icon={
            <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          }
          trend={null}
          bgColor="bg-blue-50 dark:bg-blue-900/30"
        />
        <StatCard
          title="Santé Générale"
          value="Bonne"
          icon={
            <Heart className="h-6 w-6 text-amber-600 dark:text-amber-400" />
          }
          trend={{
            value: 5,
            isPositive: true,
            label: "amélioration",
          }}
          bgColor="bg-amber-50 dark:bg-amber-900/30"
        />
      </section>

      {/* Graphiques et statistiques détaillées */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Graphique d'historique des rendez-vous */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Historique des Rendez-vous
            </h2>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                6 derniers mois
              </span>
              <BarChart2 className="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={appointmentHistory}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="#f3f4f6"
                  className="dark:stroke-gray-700"
                />
                <XAxis
                  dataKey="name"
                  stroke="#9ca3af"
                  className="dark:stroke-gray-400"
                />
                <YAxis stroke="#9ca3af" className="dark:stroke-gray-400" />
                <RechartsTooltip
                  contentStyle={tooltipContentStyle}
                  itemStyle={tooltipItemStyle}
                  labelStyle={tooltipLabelStyle}
                  wrapperStyle={{ outline: "none" }}
                />
                <Bar
                  dataKey="appointments"
                  fill="#4F46E5"
                  className="dark:fill-indigo-900/80"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Statut des ordonnances */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Statut des Ordonnances
            </h2>
            <PieChart className="h-5 w-5 text-gray-400 dark:text-gray-500" />
          </div>
          <div className="h-80 flex items-center justify-center">
            <PatientDistributionChart data={prescriptionStatus} />
          </div>
        </div>
      </div>

      {/* Métriques de santé et activité */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Évolution des métriques de santé */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Évolution des Métriques de Santé
            </h2>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                6 derniers mois
              </span>
              <Activity className="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={healthMetrics}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="#f3f4f6"
                  className="dark:stroke-gray-700"
                />
                <XAxis
                  dataKey="name"
                  stroke="#9ca3af"
                  className="dark:stroke-gray-400"
                />
                <YAxis stroke="#9ca3af" className="dark:stroke-gray-400" />
                <RechartsTooltip
                  contentStyle={tooltipContentStyle}
                  itemStyle={tooltipItemStyle}
                  labelStyle={tooltipLabelStyle}
                  wrapperStyle={{ outline: "none" }}
                />
                <Line
                  type="monotone"
                  dataKey="poids"
                  stroke="#4F46E5"
                  strokeWidth={2}
                  name="Poids (kg)"
                />
                <Line
                  type="monotone"
                  dataKey="tension"
                  stroke="#10B981"
                  strokeWidth={2}
                  name="Tension (mmHg)"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Statistiques des rendez-vous */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Statistiques Personnelles
            </h2>
          </div>
          <div className="p-6 space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-green-50 dark:bg-green-900/30 rounded-lg p-4 text-center">
                <CheckCircle className="h-6 w-6 text-green-500 dark:text-green-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  9
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  RDV Complétés
                </p>
              </div>
              <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4 text-center">
                <Clock className="h-6 w-6 text-blue-500 dark:text-blue-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  2
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  À venir
                </p>
              </div>
              <div className="bg-amber-50 dark:bg-amber-900/30 rounded-lg p-4 text-center">
                <FileText className="h-6 w-6 text-amber-500 dark:text-amber-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {prescriptions.filter((p) => p.isActive).length}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Ordonnances
                </p>
              </div>
              <div className="bg-indigo-50 dark:bg-indigo-900/30 rounded-lg p-4 text-center">
                <Heart className="h-6 w-6 text-indigo-500 dark:text-indigo-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  67kg
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Poids actuel
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Rendez-vous et ordonnances */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Prochain rendez-vous */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Prochain Rendez-vous
            </h2>
          </div>
          <div className="p-6">
            <NextAppointment
              appointment={nextAppointment}
              onCancel={handleCancelAppointment}
            />
          </div>
        </div>

        {/* Ordonnances actives */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Ordonnances Actives
            </h2>
          </div>
          <div className="p-6">
            <ActivePrescriptions
              prescriptions={prescriptions}
              onRequestRefill={handleRequestRefill}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

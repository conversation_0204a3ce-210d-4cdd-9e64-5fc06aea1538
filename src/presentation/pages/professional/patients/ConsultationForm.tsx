import { signe_vitaux } from "@/domain/models";
import { active_tab_enum } from "@/domain/models/enums";
import AddSigneVitauxModal from "@/presentation/components/common/Modal/AddSigneVitauxModal";
import { useCarnetDeSanteData } from "@/presentation/hooks/carnetDeSante";
import { PRIMARY } from "@/shared/constants/Color";
import { Box, TextField, Typography, Button } from "@mui/material";
import { useEffect, useState } from "react";

interface ConsultationFormProps {
  raisonsVisite: string;
  plaintePrincipale: string;
  remarques: string;
  examenSystemes: string;
  diagnostic: string;
  planSoins: string;
  date_consultation: Date | null;
  setRaisonsVisite: (value: string) => void;
  setPlaintePrincipale: (value: string) => void;
  setRemarques: (value: string) => void;
  setExamenSystemes: (value: string) => void;
  setDiagnostic: (value: string) => void;
  setPlanSoins: (value: string) => void;
}

export const ConsultationForm = ({
  raisonsVisite,
  plaintePrincipale,
  remarques,
  examenSystemes,
  diagnostic,
  planSoins,
  date_consultation,
  setRaisonsVisite,
  setPlaintePrincipale,
  setRemarques,
  setExamenSystemes,
  setDiagnostic,
  setPlanSoins,
}: ConsultationFormProps) => {
  const { signeVitaux } = useCarnetDeSanteData();
  const [signeVitauxFilter, setSigneVitauxFilter] =
    useState<signe_vitaux | null>(null);
  const [isAddSigneVitauxModalOpen, setIsAddSigneVitauxModalOpen] =
    useState(false);

  useEffect(() => {
    if (!date_consultation || !signeVitaux) return;

    const formattedConsultationDate = date_consultation
      .toISOString()
      .split("T")[0];

    const signe = signeVitaux.find((sv) => {
      if (!sv.date_visite) return false;
      const formattedSigneDate = new Date(sv.date_visite)
        .toISOString()
        .split("T")[0];
      return formattedSigneDate === formattedConsultationDate;
    });

    setSigneVitauxFilter(signe || null);
  }, [date_consultation, signeVitaux]);

  return (
    <>
      {signeVitauxFilter ? (
        <Box
          sx={{
            display: "flex",
            gap: 2,
            flexWrap: "wrap",
            width: "100%",
          }}
        >
          <p className="dark:text-gray-400 text-gray-600">
            <span className="dark:text-white text-black">Taille : </span>
            <span className="dark:text-gray-400 text-gray-600">
              {signeVitauxFilter.taille || "--"}
            </span>
          </p>
          <p className="dark:text-gray-400 text-gray-600">
            <span className="dark:text-white text-black">Poids : </span>
            <span className="dark:text-gray-400 text-gray-600">
              {signeVitauxFilter.poid || "--"}
            </span>
          </p>
          <p className="dark:text-gray-400 text-gray-600">
            <span className="dark:text-white text-black">Température : </span>
            <span className="dark:text-gray-400 text-gray-600">
              {signeVitauxFilter.temperature || "--"}
            </span>
          </p>
          <p className="dark:text-gray-400 text-gray-600">
            <span className="dark:text-white text-black">IMC : </span>
            <span className="dark:text-gray-400 text-gray-600">
              {signeVitauxFilter.indice_masse_corporel || "--"}
            </span>
          </p>
          <p className="dark:text-gray-400 text-gray-600">
            <span className="dark:text-white text-black">
              Circonférence de la tête :{" "}
            </span>
            <span className="dark:text-gray-400 text-gray-600">
              {signeVitauxFilter.circonference_tete || "--"}
            </span>
          </p>
          <p className="dark:text-gray-400 text-gray-600">
            <span className="dark:text-white text-black">
              Fréquence cardiaque :{" "}
            </span>
            <span className="dark:text-gray-400 text-gray-600">
              {signeVitauxFilter.frequence_cardiaque || "--"}
            </span>
          </p>
          <p className="dark:text-gray-400 text-gray-600">
            <span className="dark:text-white text-black">
              Fréquence respiratoire :{" "}
            </span>
            <span className="dark:text-gray-400 text-gray-600">
              {signeVitauxFilter.frequence_respiratoire || "--"}
            </span>
          </p>
          <p className="dark:text-gray-400 text-gray-600">
            <span className="dark:text-white text-black">SA02 : </span>
            <span className="dark:text-gray-400 text-gray-600">
              {signeVitauxFilter.sa02 || "--"}
            </span>
          </p>
          <p className="dark:text-gray-400 text-gray-600">
            <span className="dark:text-white text-black">
              Niveau de glucose :{" "}
            </span>
            <span className="dark:text-gray-400 text-gray-600">
              {signeVitauxFilter.niveau_glucose || "--"}
            </span>
          </p>
          <p className="dark:text-gray-400 text-gray-600">
            <span className="dark:text-white text-black">
              Tension artérielle :{" "}
            </span>
            <span className="dark:text-gray-400 text-gray-600">
              {signeVitauxFilter.tension_arterielle || "--"}
            </span>
          </p>
        </Box>
      ) : (
        <Box>
          <Button
            variant="contained"
            sx={{
              textTransform: "none",
              borderRadius: 5,
              backgroundColor: PRIMARY,
            }}
            onClick={() => {
              setIsAddSigneVitauxModalOpen(true);
            }}
          >
            ajouter un signe vitaux
          </Button>
          {isAddSigneVitauxModalOpen && (
            <AddSigneVitauxModal
              isAddSigneVitauxModalOpen={isAddSigneVitauxModalOpen}
              handleCloseAddSigneVitauxModal={() =>
                setIsAddSigneVitauxModalOpen(false)
              }
            />
          )}
        </Box>
      )}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Box>
          <Typography variant="subtitle2" color="textSecondary">
            Raisons pour la visite
          </Typography>
          <TextField
            placeholder="Raisons pour la visite"
            fullWidth
            size="small"
            value={raisonsVisite}
            onChange={(e) => setRaisonsVisite(e.target.value)}
          />
        </Box>
        <Box>
          <Typography variant="subtitle2" color="textSecondary">
            Plainte principale (facultatif)
          </Typography>
          <TextField
            placeholder="Plainte principale"
            fullWidth
            size="small"
            value={plaintePrincipale}
            onChange={(e) => setPlaintePrincipale(e.target.value)}
          />
        </Box>
      </div>
      <Box>
        <Typography variant="subtitle2" color="textSecondary">
          Remarques
        </Typography>
        <TextField
          placeholder="Remarques"
          multiline
          rows={2}
          fullWidth
          value={remarques}
          onChange={(e) => setRemarques(e.target.value)}
        />
      </Box>
      <Box>
        <Typography variant="subtitle2" color="textSecondary">
          Examen des systèmes (facultatif)
        </Typography>
        <TextField
          placeholder="Examen des systèmes"
          multiline
          rows={2}
          fullWidth
          value={examenSystemes}
          onChange={(e) => setExamenSystemes(e.target.value)}
        />
      </Box>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Box>
          <Typography variant="subtitle2" color="textSecondary">
            Diagnostic
          </Typography>
          <TextField
            placeholder="Diagnostic"
            multiline
            rows={2}
            fullWidth
            value={diagnostic}
            onChange={(e) => setDiagnostic(e.target.value)}
          />
        </Box>
        <Box>
          <Typography variant="subtitle2" color="textSecondary">
            Plan de soins
          </Typography>
          <TextField
            placeholder="Plan de soins"
            multiline
            rows={2}
            fullWidth
            value={planSoins}
            onChange={(e) => setPlanSoins(e.target.value)}
          />
        </Box>
      </div>
    </>
  );
};

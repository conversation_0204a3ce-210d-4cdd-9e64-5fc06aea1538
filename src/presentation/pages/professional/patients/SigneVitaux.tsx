import { useState } from "react";
import { <PERSON>, But<PERSON>, Divider } from "@mui/material";
import { PRIMARY } from "@/shared/constants/Color";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { SigneVitauxForm } from "./SigneVitauxForm";
import SigneVitauxCard from "@/presentation/components/common/historiqueCarnetSante/component/SigneVitauxCard";
import {
  useCarnetDeSante,
  useCarnetDeSanteData,
  useCarnetDeSanteState,
} from "@/presentation/hooks/carnetDeSante";
import {
  useSigneVitaux,
  useSigneVitauxForm,
} from "@/presentation/hooks/signeVitaux";

const SigneVitaux = () => {
  const { isAddForm, setIsAddForm } = useCarnetDeSanteState();
  const { idCarnetSante } = useCarnetDeSante();
  const {
    loading,
    create: handleCreateSigneVitaux,
    update: handleUpdateSigneVitaux,
  } = useSigneVitaux();
  const {
    formData,
    selectedSignes,
    selectAll,
    handleSelectAll,
    handleSigneChange,
    handleInputChange,
    getSignesVitauxData,
    resetForm,
    initialiseState,
    handlePrint,
    isFormValid,
  } = useSigneVitauxForm(idCarnetSante);

  const { signeVitaux } = useCarnetDeSanteData();
  const [idSigneVitaux, setIdSigneVitaux] = useState<number | null>(null);

  const handleSubmit = async () => {
    const data = getSignesVitauxData();
    if (idSigneVitaux) {
      await handleUpdateSigneVitaux(idSigneVitaux, data);
    } else {
      await handleCreateSigneVitaux(data);
    }
    handleBack();
  };

  const handleBack = () => {
    setIsAddForm(false);
    setIdSigneVitaux(null);
    resetForm();
  };

  const handleAddSigneVitauxButton = () => {
    setIsAddForm(true);
  };

  return (
    <div>
      {isAddForm ? (
        <>
          <div className="my-4">
            <Button
              variant="contained"
              color="primary"
              sx={{
                textTransform: "none",
                ml: 2,
                borderRadius: 5,
                backgroundColor: PRIMARY,
              }}
              onClick={handleBack}
            >
              retour
            </Button>
            <div className="flex items-center my-4">
              <h2 className="text-xl font-semibold ml-2">
                {format(new Date(), "d MMMM yyyy", {
                  locale: fr,
                })}
              </h2>
              <Button
                variant="outlined"
                sx={{
                  textTransform: "none",
                  ml: 2,
                  borderRadius: 5,
                }}
                onClick={handlePrint}
              >
                IMPRIMER
              </Button>
            </div>
          </div>

          <Box className="m-4">
            <SigneVitauxForm
              formData={formData}
              selectedSignes={selectedSignes}
              selectAll={selectAll}
              handleSelectAll={handleSelectAll}
              handleSigneChange={handleSigneChange}
              handleInputChange={handleInputChange}
            />
          </Box>

          <Divider />

          <div className="flex justify-end space-x-4 mt-4">
            <Button
              variant="outlined"
              color="primary"
              sx={{ textTransform: "none", borderRadius: 5 }}
              onClick={handleBack}
            >
              Annuler
            </Button>
            <Button
              variant="contained"
              color="primary"
              sx={{
                textTransform: "none",
                borderRadius: 5,
                backgroundColor: PRIMARY,
              }}
              onClick={handleSubmit}
              disabled={!isFormValid}
              loading={loading}
            >
              Enregistrer
            </Button>
          </div>
        </>
      ) : (
        <>
          <div className="flex items-center my-4">
            <h2 className="text-xl font-semibold">Signe vitaux</h2>
            <Button
              variant="contained"
              sx={{
                textTransform: "none",
                ml: 2,
                borderRadius: 5,
                backgroundColor: PRIMARY,
              }}
              onClick={handleAddSigneVitauxButton}
            >
              Ajouter de nouveaux signes vitaux
            </Button>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-4 md:grid-cols-3 gap-4">
            {signeVitaux?.map((signe) => (
              <SigneVitauxCard
                key={signe.id}
                onClick={() => {
                  initialiseState(signe);
                  handleAddSigneVitauxButton();
                  setIdSigneVitaux(signe.id);
                }}
                signeVitaux={signe}
                className="cursor-pointer"
              />
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default SigneVitaux;

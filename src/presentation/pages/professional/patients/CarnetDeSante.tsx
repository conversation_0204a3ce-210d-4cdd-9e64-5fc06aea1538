import { useState } from "react";
import CarnetDeSanteCard from "@/presentation/components/common/historiqueCarnetSante/component/CarnetDeSanteCard";
import { AddCarnetDeSanteModal } from "@/presentation/components/common/Modal/AddCarnetDeSanteModal";
import { useParams } from "react-router-dom";
import {
  useCarnetDeSanteData,
  useCarnetDeSante,
  useCarnetDeSanteState,
} from "@/presentation/hooks/carnetDeSante";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import { Button } from "@mui/material";
import { getDataCard } from "@/shared/constants/cardData";
import { HistoriqueCarnetDeSanteModal } from "@/presentation/components/common/Modal/HistoriqueCarnetDeSanteModal";
import { PrintService } from "@/domain/services/PrintService";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import { PRIMARY } from "@/shared/constants/Color";
import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import { useDiagnostic } from "@/presentation/hooks/carnetDeSante/sousCarnet/useDiagnostic";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useHistoriqueCarnetSante } from "@/presentation/hooks/carnetDeSante/sousCarnet/useHistoriqueCarnetSante";

const CarnetDeSante = () => {
  const [isCarnetDeSanteModalOpen, setIsCarnetDeSanteModalOpen] =
    useState(false);
  const [isHistoriqueCarnetDeSanteModal, setIsHistoriqueCarnetDeSanteModal] =
    useState(false);
  const [type, setType] = useState("");

  const { id: patientId } = useParams();
  const {
    idCarnetSante,
    loading: loadingIdCarnet,
    data,
    handleSubmit,
  } = useCarnetDeSanteData(Number(patientId));
  const { resetState } = useDiagnostic();
  const { resetSelectedFileState } = useCarnetDeSanteState();

  const { selectedDataProfessionalPatient } = useProfessionnelPatient();
  const { currentProfessional } = useSearchProfessional();

  const { loading: loadingCreatCarnet, resetSearch } = useCarnetDeSante();
  const { clearSelected } = useHistoriqueCarnetSante();
  const handleIsCarnetDeSanteModalOpen = (type: string) => {
    setIsCarnetDeSanteModalOpen(true);
    setType(type);
  };

  const handleIsHistoriqueCarnetDeSanteModalOpen = (type: string) => {
    setIsHistoriqueCarnetDeSanteModal(true);
    setType(type);
  };

  const handleCloseCarnetDeSanteModal = () => {
    setIsCarnetDeSanteModalOpen(false);
    if (type === TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage) {
      resetState();
      resetSelectedFileState();
    } else {
      resetSearch();
    }
  };

  const handleCloseHistoriqueCarnetDeSanteModal = () => {
    setIsHistoriqueCarnetDeSanteModal(false);
    // resetSearch();
    clearSelected();
  };

  const DATA_CARD = getDataCard(
    data,
    selectedDataProfessionalPatient?.patient.sexe
  );
  const DATA_CARD_DIAGNOSTICS = getDataCard(
    data,
    TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage
  );

  const handlePrint = () => {
    PrintService.printCarnetSante(
      [...DATA_CARD, ...DATA_CARD_DIAGNOSTICS],
      selectedDataProfessionalPatient?.patient,
      currentProfessional
    );
  };

  return (
    <>
      {idCarnetSante ? (
        <div>
          <div className="flex items-center my-4">
            <h2 className="text-xl font-semibold ml-2">Carnet de sante</h2>
            <Button
              variant="outlined"
              color="primary"
              sx={{ textTransform: "none", ml: 2, borderRadius: 5 }}
              onClick={handlePrint}
            >
              IMPRIMER
            </Button>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {DATA_CARD.map((data, index) => (
              <CarnetDeSanteCard
                key={index}
                data={data}
                setIsCarnetDeSanteModalOpen={() =>
                  handleIsCarnetDeSanteModalOpen(data.title)
                }
                setIsHistoriqueCarnetDeSanteModalOpen={() =>
                  handleIsHistoriqueCarnetDeSanteModalOpen(data.title)
                }
              />
            ))}
          </div>
          <div className="mt-5">
            <CarnetDeSanteCard
              data={DATA_CARD_DIAGNOSTICS[0]}
              setIsCarnetDeSanteModalOpen={() =>
                handleIsCarnetDeSanteModalOpen(
                  TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage
                )
              }
              setIsHistoriqueCarnetDeSanteModalOpen={() =>
                handleIsHistoriqueCarnetDeSanteModalOpen(
                  TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage
                )
              }
            />
          </div>
          {isCarnetDeSanteModalOpen && (
            <AddCarnetDeSanteModal
              type={type}
              isCarnetDeSanteModalOpen={isCarnetDeSanteModalOpen}
              handleCloseModal={handleCloseCarnetDeSanteModal}
            />
          )}
          {isHistoriqueCarnetDeSanteModal && (
            <HistoriqueCarnetDeSanteModal
              type={type}
              isHistoriqueCarnetDeSanteModalOpen={
                isHistoriqueCarnetDeSanteModal
              }
              handleCloseModal={handleCloseHistoriqueCarnetDeSanteModal}
            />
          )}
        </div>
      ) : (
        <div className="flex items-center justify-center">
          {loadingIdCarnet ? (
            <LoadingSpinner />
          ) : (
            <Button
              variant="contained"
              sx={{
                textTransform: "none",
                mt: 2,
                backgroundColor: PRIMARY,
              }}
              onClick={() =>
                handleSubmit({
                  id_patient: Number(patientId),
                })
              }
              loading={loadingCreatCarnet}
            >
              Creer le carnet de sante
            </Button>
          )}
        </div>
      )}
    </>
  );
};

export default CarnetDeSante;

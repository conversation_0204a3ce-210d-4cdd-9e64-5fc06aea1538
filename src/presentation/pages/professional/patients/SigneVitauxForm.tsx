import {
  Box,
  Checkbox,
  Divider,
  FormControlLabel,
  <PERSON><PERSON>ield,
  Typography,
} from "@mui/material";
import { PRIMARY } from "@/shared/constants/Color";
import { signes } from "@/presentation/hooks/signeVitaux";

interface SigneVitauxFormProps {
  formData: {
    taille: number | null;
    poids: number | null;
    temperature: number | null;
    imc: number | null;
    circonferenceTete: number | null;
    frequenceCardiaque: number | null;
    frequenceRespiratoire: number | null;
    sao2: number | null;
    glucose: number | null;
    tension_arterielle: string | null;
    diastolique: number | null;
    systolique: number | null;
  };
  selectedSignes: string[];
  selectAll: boolean;
  handleSelectAll: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleSigneChange: (
    signe: string
  ) => (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleInputChange: (
    field: string
  ) => (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export const SigneVitauxForm = ({
  formData,
  selectedSignes,
  selectAll,
  handleSelectAll,
  handleSigneChange,
  handleInputChange,
}: SigneVitauxFormProps) => {
  return (
    <div className="space-y-4">
      <Box>
        <Divider className="my-4" />
        <FormControlLabel
          control={
            <Checkbox
              size="small"
              sx={{
                color: PRIMARY,
                "&.Mui-checked": {
                  color: PRIMARY,
                },
              }}
              checked={selectAll}
              onChange={handleSelectAll}
            />
          }
          label="Sélectionner tout"
        />
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            width: "100%",
          }}
        >
          {signes.map((signe) => (
            <FormControlLabel
              key={signe}
              control={
                <Checkbox
                  checked={selectedSignes.includes(signe)}
                  onChange={handleSigneChange(signe)}
                  size="small"
                  sx={{
                    color: PRIMARY,
                    "&.Mui-checked": {
                      color: PRIMARY,
                    },
                  }}
                />
              }
              label={signe}
            />
          ))}
        </Box>
        <Divider className="my-4" />
      </Box>

      <div className="grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 gap-4 my-8">
        {selectedSignes.includes("Taille") && (
          <Box>
            <Typography variant="subtitle2" color="textSecondary">
              Taille (cm)
            </Typography>
            <TextField
              type="number"
              fullWidth
              size="small"
              value={formData.taille || ""}
              onChange={handleInputChange("taille")}
            />
          </Box>
        )}

        {selectedSignes.includes("Poids") && (
          <Box>
            <Typography variant="subtitle2" color="textSecondary">
              Poids (kg)
            </Typography>
            <TextField
              type="number"
              fullWidth
              size="small"
              value={formData.poids || ""}
              onChange={handleInputChange("poids")}
            />
          </Box>
        )}

        {selectedSignes.includes("Température") && (
          <Box>
            <Typography variant="subtitle2" color="textSecondary">
              Température (°C)
            </Typography>
            <TextField
              type="number"
              fullWidth
              size="small"
              value={formData.temperature || ""}
              onChange={handleInputChange("temperature")}
            />
          </Box>
        )}

        {selectedSignes.includes("Indice de masa corporal") && (
          <Box>
            <Typography variant="subtitle2" color="textSecondary">
              IMC
            </Typography>
            <TextField
              type="number"
              fullWidth
              size="small"
              value={formData.imc || ""}
              disabled
            />
          </Box>
        )}

        {selectedSignes.includes("Circonférence de la tête") && (
          <Box>
            <Typography variant="subtitle2" color="textSecondary">
              Circonférence de la tête (cm)
            </Typography>
            <TextField
              type="number"
              fullWidth
              size="small"
              value={formData.circonferenceTete || ""}
              onChange={handleInputChange("circonferenceTete")}
            />
          </Box>
        )}

        {selectedSignes.includes("Fréquence cardiaque") && (
          <Box>
            <Typography variant="subtitle2" color="textSecondary">
              Fréquence cardiaque (bpm)
            </Typography>
            <TextField
              type="number"
              fullWidth
              size="small"
              value={formData.frequenceCardiaque || ""}
              onChange={handleInputChange("frequenceCardiaque")}
            />
          </Box>
        )}

        {selectedSignes.includes("Fréquence respiratoire") && (
          <Box>
            <Typography variant="subtitle2" color="textSecondary">
              Fréquence respiratoire (rpm)
            </Typography>
            <TextField
              type="number"
              fullWidth
              size="small"
              value={formData.frequenceRespiratoire || ""}
              onChange={handleInputChange("frequenceRespiratoire")}
            />
          </Box>
        )}

        {selectedSignes.includes("SA02") && (
          <Box>
            <Typography variant="subtitle2" color="textSecondary">
              SA02 (%)
            </Typography>
            <TextField
              type="number"
              fullWidth
              size="small"
              value={formData.sao2 || ""}
              onChange={handleInputChange("sao2")}
            />
          </Box>
        )}

        {selectedSignes.includes("Niveau de glucose") && (
          <Box>
            <Typography variant="subtitle2" color="textSecondary">
              Niveau de glucose (mg/dL)
            </Typography>
            <TextField
              type="number"
              fullWidth
              size="small"
              value={formData.glucose || ""}
              onChange={handleInputChange("glucose")}
            />
          </Box>
        )}
      </div>
      <div>
        {selectedSignes.includes("Tension artérielle") && (
          <Box>
            <Typography variant="subtitle2" color="textSecondary">
              Tension artérielle (Systolique/Diastolique)
            </Typography>
            <div className="flex items-center gap-2">
              <TextField
                type="number"
                placeholder="Systolique"
                fullWidth
                size="small"
                value={Number(formData.systolique) || ""}
                onChange={handleInputChange("systolique")}
              />
              /
              <TextField
                type="number"
                placeholder="Diastolique"
                fullWidth
                size="small"
                value={Number(formData.diastolique) || ""}
                onChange={handleInputChange("diastolique")}
              />
            </div>
          </Box>
        )}
      </div>
    </div>
  );
};

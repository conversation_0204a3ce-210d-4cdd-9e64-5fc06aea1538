import ContactUrgence from "@/presentation/components/features/patient/registerPatienStepper/AutresInformations/ContactUrgence";
import RegisterStep1Birthday from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1Birthday";
import RegisterStep1FirstName from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1FirstName";
import RegisterStep1LastName from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1LastName";
import RegisterStep1Sexe from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1Sexe";
import RegisterStep2Adresse from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep2/RegisterStep2Adresse";
import RegisterStep2Commune from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep2/RegisterStep2Commune";
import RegisterStep2District from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep2/RegisterStep2District";
import RegisterStep2Fokontany from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep2/RegisterStep2Fokontany";
import RegisterStep3Profession from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep3/RegisterStep3Profession";
import RegisterStep3Telephones from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep3/RegisterStep3Telephones";
import { Paper, Typography } from "@mui/material";

const AddProfessionnelPatient = () => {
  return (
    <Paper sx={{ marginBottom: 3, p: 2 }}>
      <Typography variant="h6" color="green" gutterBottom>
        Creer le patient
      </Typography>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-4">
        <RegisterStep1FirstName />
        <RegisterStep1LastName />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <RegisterStep1Sexe />
        <RegisterStep1Birthday />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <RegisterStep2Adresse />
        <RegisterStep3Profession />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <RegisterStep2District />
        <RegisterStep2Commune />
        <RegisterStep2Fokontany />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <RegisterStep3Telephones />
      </div>
      <div className="grid grid-cols-1 gap-4">
        <ContactUrgence />
      </div>
    </Paper>
  );
};

export default AddProfessionnelPatient;

import FacturationDataGrid from "@/presentation/components/common/historiqueCarnetSante/component/FacturationDataGrid";
import { FacturationForm } from "@/presentation/pages/professional/patients/FacturationForm";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useFacturation } from "@/presentation/hooks/facturation";
import { useFacturationForm } from "@/presentation/hooks/facturation/useFacturationForm";
import { Box, Button, Divider } from "@mui/material";
import { useState } from "react";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { PRIMARY } from "@/shared/constants/Color";
import {
  useCarnetDeSanteData,
  useCarnetDeSanteState,
} from "@/presentation/hooks/carnetDeSante";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import { useParams } from "react-router-dom";

const Facturation = () => {
  const [facturationDetail, setFacturationDetail] = useState<{
    id: number;
    date_visite: Date;
  }>(null);
  const { isAddForm, setIsAddForm } = useCarnetDeSanteState();
  const { id: patientId } = useParams();
  const { facturations } = useCarnetDeSanteData();
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const { loading, handleCreateFacturation, handleUpdateFacturation } =
    useFacturation();

  const {
    formData,
    isFormValid,
    getFacturationData,
    resetForm,
    initialiseState,
  } = useFacturationForm(professionalId, Number(patientId));

  const { currentProfessional } = useSearchProfessional();

  const { selectedDataProfessionalPatient } = useProfessionnelPatient();

  const handlePrint = () => {
    // PrintService.printConsultation({
    //   carnet: data,
    //   signeVitaux,
    //   facturations,
    //   patient: selectedDataProfessionalPatient?.patient,
    //   professionnel: currentProfessional,
    // });
  };

  const handleSubmit = async () => {
    const data = getFacturationData();
    if (facturationDetail) {
      await handleUpdateFacturation(facturationDetail.id, data);
    } else {
      await handleCreateFacturation(data);
    }
    handleBack();
  };

  const handleBack = () => {
    resetForm();
    setIsAddForm(false);
    setFacturationDetail(null);
  };

  return (
    <div>
      {isAddForm ? (
        <div className="my-4">
          <Button
            variant="contained"
            color="primary"
            sx={{
              textTransform: "none",
              ml: 2,
              borderRadius: 5,
              backgroundColor: PRIMARY,
            }}
            onClick={handleBack}
          >
            retour
          </Button>
          <div className="flex items-center my-4">
            <h2 className="text-xl font-semibold ml-2">
              {format(
                facturationDetail
                  ? new Date(facturationDetail.date_visite)
                  : new Date(),
                "d MMMM yyyy",
                {
                  locale: fr,
                }
              )}
            </h2>
            <Button
              variant="outlined"
              sx={{
                textTransform: "none",
                ml: 2,
                borderRadius: 5,
              }}
              onClick={handlePrint}
            >
              IMPRIMER
            </Button>
          </div>
          <Divider />
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 my-4">
            <div className="space-y-6">
              <FacturationForm
                {...formData}
                dateFacturationt={
                  facturationDetail
                    ? new Date(facturationDetail.date_visite)
                    : null
                }
              />
            </div>
          </div>
          <Divider />
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "end",
              mt: 4,
            }}
          >
            <Box>
              <Button
                variant="outlined"
                color="primary"
                sx={{ textTransform: "none", ml: 2, borderRadius: 5 }}
                onClick={handleBack}
              >
                Annuler
              </Button>
              <Button
                variant="contained"
                color="primary"
                sx={{
                  textTransform: "none",
                  ml: 2,
                  borderRadius: 5,
                  backgroundColor: PRIMARY,
                }}
                onClick={handleSubmit}
                loading={loading}
                disabled={!isFormValid}
              >
                Enregistrer
              </Button>
            </Box>
          </Box>
        </div>
      ) : (
        <>
          <div className="flex items-center gap-2 my-4">
            <h2 className="text-xl font-semibold">Facturation</h2>
            <Button
              variant="contained"
              sx={{
                textTransform: "none",
                ml: 2,
                borderRadius: 5,
                backgroundColor: PRIMARY,
              }}
              onClick={() => setIsAddForm(true)}
            >
              Ajouter une nouvelle visite
            </Button>
          </div>
          <FacturationDataGrid />
        </>
      )}
    </div>
  );
};

export default Facturation;

import { signe_vitaux } from "@/domain/models";
import { useCarnetDeSanteData } from "@/presentation/hooks/carnetDeSante";
import { Box, TextField, Typography } from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { fr } from "date-fns/locale";
import { useEffect, useState } from "react";

interface FacturationFormProps {
  montant: number;
  totalPaye: number;
  recu: string;
  informations: string;
  datePaiement: Date | null;
  dateFacturationt: Date;
  setMontant: (value: number) => void;
  setTotalPaye: (value: number) => void;
  setRecu: (value: string) => void;
  setInformations: (value: string) => void;
  setDatePaiement: (value: Date | null) => void;
}

export const FacturationForm = ({
  montant,
  totalPaye,
  recu,
  informations,
  datePaiement,
  dateFacturationt,
  setMontant,
  setTotalPaye,
  setRecu,
  setInformations,
  setDatePaiement,
}: FacturationFormProps) => {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Box>
          <Typography variant="subtitle2" color="textSecondary">
            Montant du
          </Typography>
          <TextField
            type="number"
            placeholder="Montant du"
            fullWidth
            value={montant}
            onChange={(e) => setMontant(Number(e.target.value))}
          />
        </Box>
        <Box>
          <Typography variant="subtitle2" color="textSecondary">
            Total payé
          </Typography>
          <TextField
            type="number"
            placeholder="Total payé"
            fullWidth
            value={totalPaye}
            onChange={(e) => setTotalPaye(Number(e.target.value))}
          />
        </Box>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Box>
          <Typography variant="subtitle2" color="textSecondary">
            Reçu
          </Typography>
          <TextField
            placeholder="Reçu"
            fullWidth
            value={recu}
            onChange={(e) => setRecu(e.target.value)}
          />
        </Box>
        <Box>
          <Typography variant="subtitle2" color="textSecondary">
            Date du paiement
          </Typography>
          <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
            <DatePicker
              aria-hidden
              value={datePaiement}
              onChange={(newValue) => setDatePaiement(newValue)}
            />
          </LocalizationProvider>
        </Box>
      </div>
      <Box>
        <Typography variant="subtitle2" color="textSecondary">
          Visiter les informations
        </Typography>
        <TextField
          placeholder="Visiter les informations"
          multiline
          rows={2}
          fullWidth
          value={informations}
          onChange={(e) => setInformations(e.target.value)}
        />
      </Box>
    </>
  );
};

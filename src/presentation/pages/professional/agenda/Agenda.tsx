import "react-big-calendar/lib/css/react-big-calendar.css";
import "@/styles/calendar.css";
import { Box } from "@mui/material";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useAgendaState } from "@/presentation/hooks/agenda";
import {
  useAvailabilitySettings,
  useAvailabilitySettingState,
} from "@/presentation/hooks/agenda/settings";
import { useEvenement } from "@/presentation/hooks/agenda/events";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import EventPopover from "@/presentation/components/common/Popover/EventPopover";
import { AgendaCalendar } from "@/presentation/components/features/professional/agenda/component/AgendaCalendar";
import { AgendaModals } from "@/presentation/components/features/professional/agenda/component/AgendaModals";
import { useEffect } from "react";

const Agenda = () => {
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );

  const { settings, getSettings } = useAvailabilitySettings();
  const { evenements, fetchEvenements } = useEvenement();
  const { initializeSettingsState } = useAvailabilitySettingState();
  const { appointmentProfessional, fetchAppointmentListByProfessionalId } =
    useConsultationState();

  const { isSaveSettings, selectedEvent, settingsLocal } = useAgendaState(
    settings,
    evenements,
    appointmentProfessional
  );

  useEffect(() => {
    if (professionalId) {
      getSettings(professionalId);
      fetchEvenements(professionalId);
      fetchAppointmentListByProfessionalId(professionalId);
    }
  }, [isSaveSettings]);

  useEffect(() => {
    if (settings) {
      initializeSettingsState(settings, settingsLocal);
    }
  }, [settings, settingsLocal]);

  return (
    <Box>
      <AgendaCalendar />
      {selectedEvent && <EventPopover />}
      <AgendaModals />
    </Box>
  );
};

export default Agenda;

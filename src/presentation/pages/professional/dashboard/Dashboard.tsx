import { useAppSelector } from "@/presentation/hooks/redux";
import {
  Calendar,
  TrendingUp,
  Users,
  Clock,
  Activity,
  CheckCircle,
  AlertCircle,
  Bar<PERSON>hart2,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Bar,
  BarC<PERSON>,
} from "recharts";

// Composants
import StatCard from "@/presentation/components/common/StatCard";
import AppointmentList from "@/presentation/components/features/professional/appointment/AppointmentList";
import UpcomingAppointments from "@/presentation/components/features/professional/appointment/UpcomingAppointments";
import PatientDistribution<PERSON>hart from "@/presentation/components/features/professional/appointment/stats/PatientDistributionChart";

// Hooks
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useDashboardData } from "@/presentation/hooks/dashboard/professionnel/use-dashboard-data";
import { useEffect } from "react";
import { useDarkMode } from "@/presentation/hooks/use-dark-mode";
import { useAvailabilitySettings } from "@/presentation/hooks/agenda/settings";

const Dashboard = () => {
  const { searchProfessionalById } = useSearchProfessional();
  const { getSettings } = useAvailabilitySettings();
  const isDarkMode = useDarkMode();
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const professionalName = useAppSelector(
    (state) => state.authentification.userData?.nom
  );

  // Utiliser notre hook personnalisé pour récupérer toutes les données du dashboard
  const {
    patientMetrics,
    revenue,
    revenueData,
    patientDistribution,
    appointmentStats,
    todayAppointments,
    completionRate,
  } = useDashboardData(professionalId);

  // Définir les styles du tooltip en fonction du mode
  const tooltipContentStyle = isDarkMode
    ? {
        backgroundColor: "rgba(31, 41, 55, 0.9)", // dark:bg-gray-800
        borderColor: "#374151", // dark:border-gray-700
        color: "#f9fafb", // dark:text-gray-100
      }
    : {
        backgroundColor: "rgba(255, 255, 255, 0.9)",
        borderColor: "#e5e7eb",
        color: "#111827",
      };

  const tooltipItemStyle = isDarkMode
    ? { color: "#f3f4f6" } // dark:text-gray-200
    : { color: "#111827" };

  const tooltipLabelStyle = isDarkMode
    ? { color: "#f3f4f6" } // dark:text-gray-200
    : { color: "#111827" };

  // Charger les données supplémentaires
  useEffect(() => {
    if (professionalId) {
      getSettings(professionalId);
      searchProfessionalById({ id: professionalId });
    }
  }, [professionalId]);

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* En-tête avec salutation */}
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Bonjour, {professionalName || "Docteur"}
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Voici un aperçu de votre activité professionnelle
        </p>
      </header>

      {/* Cartes de statistiques principales */}
      <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Patients Total"
          value={patientMetrics?.totalPatients || 0}
          icon={
            <Users className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
          }
          trend={null}
          bgColor="bg-indigo-50 dark:bg-indigo-900/30"
        />
        <StatCard
          title="Nouveaux Patients"
          value={patientMetrics?.newPatients || 0}
          icon={
            <TrendingUp className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
          }
          trend={{
            value: patientMetrics?.trends?.newPatients || 0,
            isPositive: true,
            label: "ce mois-ci",
          }}
          bgColor="bg-emerald-50 dark:bg-emerald-900/30"
        />
        <StatCard
          title="Revenus du Mois"
          value={`${revenue.monthly} MGA`}
          icon={
            <Activity className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          }
          trend={{
            value: revenue.trend.percentage,
            isPositive: revenue.trend.isPositive,
            label: "vs mois dernier",
          }}
          bgColor="bg-blue-50 dark:bg-blue-900/30"
        />
        <StatCard
          title="Taux de Complétion"
          value={`${completionRate}%`}
          icon={
            <CheckCircle className="h-6 w-6 text-amber-600 dark:text-amber-400" />
          }
          trend={null}
          bgColor="bg-amber-50 dark:bg-amber-900/30"
        />
      </section>

      {/* Graphiques et statistiques détaillées */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Graphique de revenus */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Évolution des Revenus
            </h2>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                6 derniers mois
              </span>
              <BarChart2 className="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={revenueData}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="#f3f4f6"
                  className="dark:stroke-gray-700"
                />
                <XAxis
                  dataKey="name"
                  stroke="#9ca3af"
                  className="dark:stroke-gray-400"
                />
                <YAxis stroke="#9ca3af" className="dark:stroke-gray-400" />
                <RechartsTooltip
                  contentStyle={tooltipContentStyle}
                  itemStyle={tooltipItemStyle}
                  labelStyle={tooltipLabelStyle}
                  wrapperStyle={{ outline: "none" }}
                />
                <Bar
                  dataKey="revenue"
                  fill="#4F46E5"
                  className="dark:fill-indigo-900/80"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Distribution des patients */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Distribution des Patients
            </h2>
            <PieChart className="h-5 w-5 text-gray-400 dark:text-gray-500" />
          </div>
          <div className="h-80 flex items-center justify-center">
            <PatientDistributionChart data={patientDistribution} />
          </div>
        </div>
      </div>

      {/* Rendez-vous et activité */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Rendez-vous du jour */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Rendez-vous du Jour
              </h2>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {new Date().toLocaleDateString("fr-FR", {
                    weekday: "long",
                    day: "numeric",
                    month: "long",
                  })}
                </span>
                <Calendar className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              </div>
            </div>
          </div>
          <div className="p-6">
            <AppointmentList appointments={todayAppointments} />
          </div>
        </div>

        {/* Statistiques des rendez-vous */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Statistiques des Rendez-vous
            </h2>
          </div>
          <div className="p-6 space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-green-50 dark:bg-green-900/30 rounded-lg p-4 text-center">
                <CheckCircle className="h-6 w-6 text-green-500 dark:text-green-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {appointmentStats.completed}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Complétés
                </p>
              </div>
              <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4 text-center">
                <Clock className="h-6 w-6 text-blue-500 dark:text-blue-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {appointmentStats.upcoming}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  À venir
                </p>
              </div>
              <div className="bg-amber-50 dark:bg-amber-900/30 rounded-lg p-4 text-center">
                <AlertCircle className="h-6 w-6 text-amber-500 dark:text-amber-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {appointmentStats.cancelled}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Annulés
                </p>
              </div>
              <div className="bg-indigo-50 dark:bg-indigo-900/30 rounded-lg p-4 text-center">
                <Calendar className="h-6 w-6 text-indigo-500 dark:text-indigo-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {appointmentStats.totalThisMonth}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Total du mois
                </p>
              </div>
            </div>

            <div className="mt-6">
              <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                Prochains Rendez-vous
              </h3>
              <UpcomingAppointments
                appointments={todayAppointments.slice(0, 2)}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

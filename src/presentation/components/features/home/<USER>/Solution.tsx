import imagenum from "@/assets/numeric.png";
import imagedoc from "@/assets/document.png";
import imagecal from "@/assets/calendar.png";
import { Link } from "react-router-dom";

import { motion } from "framer-motion";

const solutions = [
  {
    imageUrl: imagenum,
    title: "Prenez rendez-vous avec vos professionnels de santé",
    description:
      "Consultez votre docteur, médecin généraliste, spécialiste (dentiste, dermatologue, gynécologue, cardiologue, pédiatre, ophtalmologue...) ou professionnel paramédical (kinésithérapeute, orthophoniste, orthoptiste, osthéopathe, diététicien, podologue...).",
    button: "Prendre rendez-vous",
  },
  {
    imageUrl: imagedoc,
    title: "Carnet de santé numerique (CSN): vos documents de santé toujours avec vous !",
    description:
      "Conservez vos ordonnances, résultats d'examen et certificats dans un environnement sécurisé. Partagez-les en direct avec vos praticiens avant, pendant ou après vos rendez-vous médicaux.",
    button: "Accéder à mes documents"
  }
  // },
  // {
  //   imageUrl: imagecal,
  //   title:
  //     "Trouvez un praticien disponible aujourd'hui, même le samedi, avec MEDDoC",
  //   description:
  //     "Besoin d'une consultation rapidement? Prenez rendez-vous avec un médecin, un dentiste ou un thérapeute disponible le jour-même. En quelques clics, trouvez un professionnel de santé proche de chez vous ou proposant des téléconsultation pour un accès rapide aux soins.",
  //   button: "Prendre rendez-vous maintenant",
  // },
];

const Solution = () => {
  return (
    <>
      {solutions.map((item, index) => (
        <motion.div 
          key={index}
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="grid grid-cols-1 md:grid-cols-2 items-center gap-6 rounded-lg p-2 pl-6 pr-6"
        >
          {index % 2 == 0 ? (
            <>
              <div className="p-2">
                <motion.h1 
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                  className="text-3xl text-meddoc-fonce font-bold mb-4"
                >{item.title}</motion.h1>
                <motion.p
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >{item.description}</motion.p>
                <br />
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  <button className="p-2 bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white font-medium px-6 py-3 rounded-lg shadow-md flex items-center justify-center">{item.button}</button>
                </motion.div>
              </div>
              <motion.div 
                className="flex justify-center"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <img src={item.imageUrl} alt="Dr. Mohamed" width={300} />
              </motion.div>
            </>
          ) : (
            <>
              <motion.div 
                className="flex justify-center"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <img src={item.imageUrl} alt="Dr. Mohamed" width={300} />
              </motion.div>
              <div className="p-2">
                <motion.h1 
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                  className="text-3xl text-meddoc-fonce font-bold mb-4"
                >{item.title}</motion.h1>
                <motion.p
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >{item.description}</motion.p>
                <br />
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  <button className="p-2 bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white font-medium px-6 py-3 rounded-lg shadow-md flex items-center justify-center">{item.button}</button>
                </motion.div>
              </div>
            </>
          )}
        </motion.div>
      ))}
    </>
  );
};

export default Solution;

import React, { useEffect, useState } from 'react'

const textsToWrite = [
  'sage-femme',
  'cardiologue',
  'médecin',
  'dentiste',
  'psychologue',
  'dermatologue',
  'ostéopathe',
  'gynécologue'
] // Liste des textes

const AutoWrite: React.FC = () => {
  const [displayedText, setDisplayedText] = useState('')
  const [cursorVisible, setCursorVisible] = useState(true)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isDeleting, setIsDeleting] = useState(false) // Contrôle de l'effet de suppression

  useEffect(() => {
    let interval: NodeJS.Timeout
    const currentText = textsToWrite[currentIndex]

    if (!isDeleting) {
      // Écriture progressive
      interval = setInterval(() => {
        setDisplayedText((prev) => {
          if (prev.length < currentText.length) {
            return currentText.slice(0, prev.length + 1)
          } else {
            clearInterval(interval)
            setTimeout(() => setIsDeleting(true), 1000) // Pause avant suppression
            return prev
          }
        })
      }, 100)
    } else {
      // Suppression progressive
      interval = setInterval(() => {
        setDisplayedText((prev) => {
          if (prev.length > 0) {
            return prev.slice(0, -1)
          } else {
            clearInterval(interval)
            setIsDeleting(false)
            setCurrentIndex((prevIndex) => (prevIndex + 1) % textsToWrite.length) // Texte suivant
            return prev
          }
        })
      }, 100)
    }

    return () => clearInterval(interval)
  }, [textsToWrite, currentIndex, isDeleting])

  useEffect(() => {
    const cursorBlink = setInterval(() => {
      setCursorVisible((prev) => !prev)
    }, 500) // Clignotement du curseur (500ms)
    return () => clearInterval(cursorBlink)
  }, [])

  return (
    <span className="bg-clip-text bg-gradient-to-r from-meddoc-primary to-meddoc-primary/80">
      {displayedText}
      <span
        className={`${cursorVisible ? 'opacity-100' : 'opacity-0'
          } inline-block w-1 h-6 bg-white animate-blink`}
      ></span>
       
    </span>
  )
}

export default AutoWrite

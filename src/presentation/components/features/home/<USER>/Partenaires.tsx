import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import styled from 'styled-components';
import { motion } from 'framer-motion';

const PartenairesSection = styled.section`
  padding: 2rem 0;
  background-color: white;
  
  h2 {
    text-align: center;
    font-size: 2.5rem;
    // color: #2d3748;
    margin-bottom: 3rem;
    font-weight: 600;
  }
`;

const PartenairesContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  
  .swiper {
    padding: 2rem 0;
  }
  
  .swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  }
  
  .partner-image {
    max-width: 180px;
    height: auto;
    transition: all 0.3s ease;
    
    &:hover {
      filter: grayscale(0%);
      opacity: 1;
    }
  }
`;

const Partenaires = () => {
  const partners = [
    {
      id: 1,
      name: "Hack@ma",
      image: "/partenaires/<EMAIL>",

    },
    {
      id: 2,
      name: "AALCUP",
      image: "/partenaires/aalcup.png",
      link: "https://tiec.gov.eg/English/Programs/AAL/Pages/default.aspx"
    },
    {
      id: 3,
      name: "Africa by inclube me",
      image: "/partenaires/africa.png",

    },
    {
      id: 4,
      name: "AWS",
      image: "/partenaires/aws.png",
      link: "https://aws.amazon.com/fr/"
    },
    {
      id: 5,
      name: "Cap Digital",
      image: "/partenaires/cap-digital.jpg",
      link: "https://www.capdigital.com/"
    },
    {
      id: 6,
      name: "CUA",
      image: "/partenaires/cua.png",
      link: "https://www.cua.mg/?fbclid=IwY2xjawJsfLJleHRuA2FlbQIxMAABHrlN-gpQfFjibqTif1i2dwkhKqfZAbDzfNwXDs_aARcyeE34vs5Lg_7eF8ep_aem_kgUQbl7UUUArUCubqEFM7Q"
    },
    {
      id: 7,
      name: "Econnect",
      image: "/partenaires/econnect.jpg" ,
      link: "https://www.e-connect.africa/"
    },
    {
      id: 8,
      name: "EMC",
      image: "/partenaires/emc.jpg",
      link: "https://www.facebook.com/emcmadagascar"
    },
    {
      id: 9,
      name: "Miary Digital",
      image: "/partenaires/miary.jpg",
      link: "https://digital.miary.mg/"
    },
    {
      id: 10,
      name: "Orange fab",
      image: "/partenaires/orange.png",
      link: "https://orangefab.mg/fr/"
    },
    {
      id: 11,
      name: "GSPS",
      image: "/partenaires/sgps.png",
      link: "https://www.facebook.com/profile.php?id=100083087205002"
    },
    {
      id: 12,
      name: "Shop plus",
      image: "/partenaires/shop-plus.png",
      link: "https://shopsplusproject.org/where-we-work/africa/madagascar"
    },
    {
      id: 13,
      name: "Zafytody",
      image: "/partenaires/zafytody.png",
      link: "https://zafytody.mg/"
    }
  ];

  return (
    <PartenairesSection>
      <PartenairesContainer>
      <motion.h2 
          className="text-3xl text-meddoc-fonce font-bold"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          Ils nous soutiennent
        </motion.h2>
        <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
        <Swiper
          modules={[Autoplay, Pagination]}
          spaceBetween={30}
          slidesPerView={1}
          autoplay={{
            delay: 3000,
            disableOnInteraction: false,
          }}
          pagination={{ clickable: true }}
          breakpoints={{
            640: {
              slidesPerView: 2,
            },
            768: {
              slidesPerView: 3,
            },
            1024: {
              slidesPerView: 4,
            },
          }}
        >
          {partners.map((partner) => (
           
            <SwiperSlide key={partner.id}>
              {partner.link ? (
                <a 
                  href={partner.link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  style={{ display: 'block' }}
                >
                  <img
                    src={partner.image}
                    alt={partner.name}
                    className="partner-image"
                  />
                </a>
              ) : (
                <img
                  src={partner.image}
                  alt={partner.name}
                  className="partner-image"
                />
              )}
            </SwiperSlide>
          
          ))}
        </Swiper>
        </motion.div>
      </PartenairesContainer>
    </PartenairesSection>
  );
};

export default Partenaires;
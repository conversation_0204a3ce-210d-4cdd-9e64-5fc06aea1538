import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import Allergies from "@/presentation/components/common/historiqueCarnetSante/allergies/Allergies";
import Medicaments from "@/presentation/components/common/historiqueCarnetSante/medicament/Medicament";
import AffectationMedicales from "@/presentation/components/common/historiqueCarnetSante/affectationMedicales/AffectationMedicales";
import DispositifMedicaux from "@/presentation/components/common/historiqueCarnetSante/dispositifMedicaux/DispositifMedicaux";
import AntecedantChirurgicaux from "@/presentation/components/common/historiqueCarnetSante/antecedantChirurgicaux/AntecedantChirurgicaux";
import AntecedantFamilliaux from "@/presentation/components/common/historiqueCarnetSante/antecedantFamilliaux/AntecedantFamilliaux";
import AntecedentsSociaux from "@/presentation/components/common/historiqueCarnetSante/antecedentsSociaux/AntecedentsSociaux";
import Vaccination from "@/presentation/components/common/historiqueCarnetSante/vaccination/Vaccination";
import AntecedentGrossesse from "@/presentation/components/common/historiqueCarnetSante/antecedentGrossesse/AntecedentGrossesse";
import { useHandleCarnetSanteState } from "@/presentation/hooks/carnetDeSante/sousCarnet/useHandleCarnetSanteState";
import { useEffect } from "react";
import ConditionGynecologique from "@/presentation/components/common/historiqueCarnetSante/conditionGynecologique/ConditionGynecologique";
import TestsMedicauxDiagnosticsEtDepistage from "@/presentation/components/common/historiqueCarnetSante/testsMedicauxDiagnosticsEtDepistage/TestsMedicauxDiagnosticsEtDepistage";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";

interface RenderFormFieldsProps {
  type: string;
  item?: string;
  itemId?: number;
}

export const RenderFormFields = ({
  item,
  type,
  itemId,
}: RenderFormFieldsProps) => {
  const { initialiseState } = useHandleCarnetSanteState();

  useEffect(() => {
    if (itemId) {
      initialiseState(item, itemId, type);
    }
  }, [itemId]);

  switch (type) {
    case TITRES_CARNET_DE_SANTE.allergies:
      return <Allergies item={item} />;
    case TITRES_CARNET_DE_SANTE.medicaments:
      return <Medicaments item={item} />;
    case TITRES_CARNET_DE_SANTE.affectationMedicales:
      return <AffectationMedicales item={item} />;
    case TITRES_CARNET_DE_SANTE.dispositifMedicaux:
      return <DispositifMedicaux item={item} />;
    case TITRES_CARNET_DE_SANTE.antecedantChirurgicaux:
      return <AntecedantChirurgicaux item={item} />;
    case TITRES_CARNET_DE_SANTE.antecedantFamiliaux:
      return <AntecedantFamilliaux item={item} />;
    case TITRES_CARNET_DE_SANTE.antecedentsSociaux:
      return <AntecedentsSociaux item={item} />;
    case TITRES_CARNET_DE_SANTE.vaccination:
      return <Vaccination item={item} />;
    case TITRES_CARNET_DE_SANTE.antecedentGrossesse:
      return <AntecedentGrossesse item={item} />;
    case TITRES_CARNET_DE_SANTE.conditionGynecologique:
      return <ConditionGynecologique item={item} />;
    case TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage:
      return <TestsMedicauxDiagnosticsEtDepistage />;
    default:
      return <p>Aucune rendue disponible</p>;
  }
};

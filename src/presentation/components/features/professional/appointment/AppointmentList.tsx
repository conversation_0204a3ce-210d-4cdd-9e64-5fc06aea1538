import { FC } from "react";
import { Clock, User, Tag } from "lucide-react";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { useAvailabilitySettings } from "@/presentation/hooks/agenda/settings";

interface AppointmentListProps {
  appointments: AppointmentProfessionalDTO[];
}

const AppointmentList: FC<AppointmentListProps> = ({ appointments }) => {
  const { settings } = useAvailabilitySettings();
  if (appointments.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          Aucun rendez-vous prévu aujourd'hui
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {appointments.map((appointment) => (
        <div
          key={appointment?.id}
          className={`p-4 rounded-lg border ${
            appointment?.statut === rendez_vous_statut_enum.A_VENIR
              ? "border-green-100 bg-green-50 dark:border-green-800 dark:bg-green-900/20"
              : "border-amber-100 bg-amber-50 dark:border-amber-800 dark:bg-amber-900/20"
          }`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div
                className={`p-2 rounded-full ${
                  appointment?.statut === rendez_vous_statut_enum.A_VENIR
                    ? "bg-green-100 dark:bg-green-800/50"
                    : "bg-amber-100 dark:bg-amber-800/50"
                }`}
              >
                <User
                  className={`h-5 w-5 ${
                    appointment?.statut === rendez_vous_statut_enum.A_VENIR
                      ? "text-green-600 dark:text-green-400"
                      : "text-amber-600 dark:text-amber-400"
                  }`}
                />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">
                  {appointment?.patient.nom} {appointment?.patient.prenom}
                </h3>
                <div className="flex items-center mt-1 text-sm text-gray-600 dark:text-gray-400">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{appointment?.time}</span>
                  <span className="mx-2">•</span>
                  <span>{settings?.temps_moyen_consulation} min</span>
                </div>
              </div>
            </div>
            <div>
              <span
                className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                  appointment?.statut === rendez_vous_statut_enum.A_VENIR
                    ? "bg-green-100 text-green-800 dark:bg-green-800/50 dark:text-green-300"
                    : "bg-amber-100 text-amber-800 dark:bg-amber-800/50 dark:text-amber-300"
                }`}
              >
                <Tag className="h-3 w-3 mr-1" />
                {appointment?.statut}
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default AppointmentList;

import { Chip, styled } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import { Professionnel } from "@/domain/models";
import { StethoscopeIcon } from "lucide-react";
import { useSearchParams } from "react-router-dom";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";

// Création d'un Chip personnalisé avec texte blanc
const StyledChip = styled(Chip)({
  "& .MuiChip-label": {
    color: "white",
    fontWeight: 500,
  },
  "& .MuiChip-icon": {
    color: "white",
  }
});

// Composant Chip animé avec Framer Motion
const WhiteTextChip = ({
  icon,
  label,
  className,
  size,
  delay = 0,
  ...props
}: {
  icon?: React.ReactElement;
  label: string | number | React.ReactNode;
  className?: string;
  size?: "small" | "medium";
  delay?: number;
  [key: string]: any;
}) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.8, y: 20 }}
    animate={{ opacity: 1, scale: 1, y: 0 }}
    transition={{
      duration: 0.5,
      delay: delay,
      type: "spring",
      stiffness: 100
    }}
  >
    <StyledChip
      icon={icon}
      label={label}
      className={className}
      size={size}
      {...props}
    />
  </motion.div>
);

interface SearchResultsProps {
  professionals: Professionnel[];
  filters: {
    status: string;
    gender: string;
    language: string;
  };
}

const SearchResults = ({ professionals, filters }: SearchResultsProps) => {
  const [searchParams, _] = useSearchParams();

  const [keyword, setKeyword] = useState("");
  const [location, setLocation] = useState("");

  useEffect(() => {
    const keyword = searchParams.get("searchname");
    const location = searchParams.get("searchlocation");

    setKeyword(keyword || "");
    setLocation(location || "");
  }, [searchParams]);

  if (professionals.length === 0) return null;

  return (
    <div className="w-full flex bg-white mx-auto">
      <div className="px-4 py-2">
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <WhiteTextChip
              icon={
                <StethoscopeIcon
                  width={17}
                  height={17}
                  className="!text-white"
                />
              }
              label={`${professionals.length} résultat${professionals.length > 1 ? "s" : ""}`}
              className="bg-gradient-to-r from-meddoc-fonce to-meddoc-primary"
              size="medium"
              delay={0}
            />
            <div className="h-4 w-[1px] bg-gray-300 mx-2" />
            <div className="flex gap-2 flex-wrap">
              {keyword && (
                <WhiteTextChip
                  icon={
                    <SearchIcon className="!text-white" fontSize="small" />
                  }
                  size="medium"
                  label={keyword}
                  className="bg-gradient-to-r from-meddoc-fonce to-meddoc-primary"
                  delay={0.1}
                />
              )}
              {location && (
                <WhiteTextChip
                  icon={
                    <LocationOnIcon
                      className="!text-white"
                      fontSize="small"
                    />
                  }
                  size="medium"
                  label={location}
                  className="bg-gradient-to-r from-meddoc-fonce to-meddoc-primary"
                  delay={0.2}
                />
              )}
            </div>
          </div>
          {(filters.status || filters.gender) && (
            <div className="flex items-center gap-2">
              <motion.span
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.3 }}
                className="text-sm font-medium text-meddoc-primary"
              >
                Filtres actifs :
              </motion.span>
              {filters.status && (
                <WhiteTextChip
                  size="small"
                  label={
                    filters.status === "accepte"
                      ? "Accepte nouveaux patients"
                      : "Liste d'attente"
                  }
                  className="bg-gradient-to-r from-meddoc-primary to-meddoc-secondary"
                  delay={0.35}
                />
              )}
              {filters.gender && (
                <WhiteTextChip
                  size="medium"
                  label={filters.gender === "F" ? "Femme" : "Homme"}
                  className="bg-gradient-to-r from-meddoc-primary to-meddoc-secondary"
                  delay={0.4}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SearchResults;

import { FormControlLabel, RadioGroup, Radio, Paper } from "@mui/material";
import { categorie_enum } from "@/domain/models/enums";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";

const BookAppointment = () => {
  const { categorie, handleCategorieChange } = useConsultationState();

  return (
    <Paper sx={{ marginBottom: 3, p: 2 }}>
      <RadioGroup
        value={categorie || categorie_enum["consultation de suivi"]}
        onChange={(e) => handleCategorieChange(e.target.value)}
      >
        <FormControlLabel
          value={categorie_enum["consultation de suivi"]}
          control={<Radio />}
          label="Patient existant"
        />
        <FormControlLabel
          value={categorie_enum["premiere consultation"]}
          control={<Radio />}
          label="Nouveau Patient"
        />
      </RadioGroup>
    </Paper>
  );
};

export default BookAppointment;

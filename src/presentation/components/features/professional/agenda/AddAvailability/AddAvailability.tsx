import { Box, Divider } from "@mui/material";
import { Availability } from "./component/Availability";
import { Planning } from "./component/Planning";
import { SettingRDV } from "./component/SettingRDV";
import { useAvailabilitySettingState } from "@/presentation/hooks/agenda/settings";
import { AvailableExeptionDate } from "./component/AvailableExeptionDate";
import Duration from "./component/Duration";

const AddAvailability = () => {
  const { settings } = useAvailabilitySettingState();

  return (
    <Box>
      {/* Durée des rendez-vous */}
      <Duration />
      <Divider sx={{ my: 3 }} />

      {/* Disponibilité habituelle */}
      <Availability />
      <Divider sx={{ my: 3 }} />

      {/* Période de planification */}
      <Planning />

      {/* Disponibilité à certaines dates */}
      {settings.type != "specifique" && <AvailableExeptionDate />}

      {/* Paramètres des rendez-vous réservés */}
      <SettingRDV />
    </Box>
  );
};

export default AddAvailability;

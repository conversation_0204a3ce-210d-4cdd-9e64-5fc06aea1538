import React, { useState } from "react";
import { <PERSON>ton, <PERSON>po<PERSON>, <PERSON>, TextField, IconButton } from "@mui/material";
import { CirclePlus, CircleX, Calendar } from "lucide-react";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import DatePickerModal from "@/presentation/components/common/Modal/DatePickerModal";
import { useAvailabilitySettingState } from "@/presentation/hooks/agenda/settings";

export const AvailableExeptionDate = () => {
  const {
    settings,
    errors,
    handleAddDate,
    handleDateStartTimeChange,
    handleDateEndTimeChange,
    handleDeleteDateTimeSlot,
    handleAddDateTimeSlot,
  } = useAvailabilitySettingState();

  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const handleOpenDatePicker = () => {
    setIsDatePickerOpen(true);
  };
  const handleCloseDatePicker = () => {
    setIsDatePickerOpen(false);
  };
  const handleDateSelect = (date: Date) => {
    handleAddDate(date);
  };

  return (
    <Box sx={{ my: 4 }}>
      <Typography variant="subtitle1" gutterBottom>
        Disponibilité à certaines dates
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        Indique vos disponibilités à certaines dates
      </Typography>
      <Box>
        {settings.horaire_date_specifique &&
          settings.horaire_date_specifique.map((schedule, dateIndex) => (
            <Box key={dateIndex} sx={{ mb: 3 }}>
              <Box
                sx={{ display: "flex", alignItems: "center", gap: 4, my: 2 }}
              >
                <Typography variant="subtitle2">
                  {schedule.date instanceof Date
                    ? format(schedule.date, "dd MMM. yyyy", { locale: fr })
                    : format(new Date(schedule.date), "dd MMMM yyyy", {
                        locale: fr,
                      })}
                </Typography>
                {schedule.creneau_horaire.length === 0 && (
                  <>
                    <Typography color="text.secondary">Indisponible</Typography>
                    <IconButton
                      onClick={() => handleAddDateTimeSlot(dateIndex)}
                      size="small"
                      sx={{
                        "&:hover": { backgroundColor: "transparent" },
                      }}
                    >
                      <CirclePlus />
                    </IconButton>
                  </>
                )}
              </Box>

              {schedule.creneau_horaire.map((timeSlot, timeSlotIndex) => (
                <Box
                  key={timeSlotIndex}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                    ml: 2,
                    mb: 1,
                  }}
                >
                  <TextField
                    type="time"
                    value={timeSlot.heure_debut}
                    onChange={(e) =>
                      handleDateStartTimeChange(
                        dateIndex,
                        timeSlotIndex,
                        e.target.value
                      )
                    }
                    size="small"
                  />
                  <Typography>-</Typography>
                  <TextField
                    type="time"
                    value={timeSlot.heure_fin}
                    onChange={(e) =>
                      handleDateEndTimeChange(
                        dateIndex,
                        timeSlotIndex,
                        e.target.value
                      )
                    }
                    size="small"
                  />
                  <Box>
                    <IconButton
                      onClick={() =>
                        handleDeleteDateTimeSlot(dateIndex, timeSlotIndex)
                      }
                      size="small"
                      sx={{ "&:hover": { backgroundColor: "transparent" } }}
                    >
                      <CircleX />
                    </IconButton>
                    {timeSlotIndex === 0 && (
                      <>
                        <IconButton
                          onClick={() => handleAddDateTimeSlot(dateIndex)}
                          size="small"
                          sx={{ "&:hover": { backgroundColor: "transparent" } }}
                        >
                          <CirclePlus />
                        </IconButton>
                      </>
                    )}
                  </Box>
                </Box>
              ))}
            </Box>
          ))}

        <Button
          startIcon={<Calendar />}
          onClick={handleOpenDatePicker}
          variant="outlined"
          sx={{ mt: 2, textTransform: "none" }}
        >
          Ajouter une date
        </Button>

        <DatePickerModal
          open={isDatePickerOpen}
          onClose={handleCloseDatePicker}
          onDateSelect={handleDateSelect}
        />
      </Box>
    </Box>
  );
};

import { TextField, Typography, Box, IconButton } from "@mui/material";
import { CirclePlus, CircleX, Copy } from "lucide-react";
import { useAvailabilitySettingState } from "@/presentation/hooks/agenda/settings";

const RenderWeeklySelector = () => {
  const {
    settings,
    errors,
    handleStartTimeChange,
    handleEndTimeChange,
    handleAddTimeSlot,
    handleDeleteTimeSlot,
    handleCopyTimeSlot,
  } = useAvailabilitySettingState();
  return (
    <>
      {settings &&
        settings.horaire_hebdomadaire.map((day, dayIndex) => (
          <Box
            key={dayIndex}
            sx={{ display: "flex", flexDirection: "column", mb: 2 }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 1 }}>
              <Typography sx={{ width: 100 }}>{day.jour}</Typography>
              {day.creneau_horaire.length === 0 && (
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography color="text.secondary">Indisponible</Typography>
                  <IconButton
                    onClick={() => handleAddTimeSlot(dayIndex)}
                    size="small"
                    sx={{
                      "&:hover": { backgroundColor: "transparent" },
                      ml: "auto",
                    }}
                  >
                    <CirclePlus />
                  </IconButton>
                </Box>
              )}
            </Box>

            {day.creneau_horaire.map((timeSlot, timeSlotIndex) => (
              <Box
                key={timeSlotIndex}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  ml: 12,
                  mb: 1,
                }}
              >
                <TextField
                  type="time"
                  value={timeSlot.heure_debut}
                  onChange={(e) =>
                    handleStartTimeChange(
                      dayIndex,
                      timeSlotIndex,
                      e.target.value
                    )
                  }
                  size="small"
                />
                <Typography>à</Typography>
                <TextField
                  type="time"
                  value={timeSlot.heure_fin}
                  onChange={(e) =>
                    handleEndTimeChange(dayIndex, timeSlotIndex, e.target.value)
                  }
                  size="small"
                />
                <Box>
                  <IconButton
                    onClick={() =>
                      handleDeleteTimeSlot(dayIndex, timeSlotIndex)
                    }
                    size="small"
                    sx={{ "&:hover": { backgroundColor: "transparent" } }}
                  >
                    <CircleX />
                  </IconButton>
                  {timeSlotIndex === 0 && (
                    <>
                      <IconButton
                        onClick={() => handleAddTimeSlot(dayIndex)}
                        size="small"
                        sx={{ "&:hover": { backgroundColor: "transparent" } }}
                      >
                        <CirclePlus />
                      </IconButton>

                      <IconButton
                        onClick={() => handleCopyTimeSlot(dayIndex)}
                        size="small"
                        sx={{ "&:hover": { backgroundColor: "transparent" } }}
                      >
                        <Copy />
                      </IconButton>
                    </>
                  )}
                </Box>
              </Box>
            ))}
          </Box>
        ))}
    </>
  );
};

export default RenderWeeklySelector;

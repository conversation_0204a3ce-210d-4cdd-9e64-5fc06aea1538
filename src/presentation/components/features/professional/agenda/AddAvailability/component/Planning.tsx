import React, { useState } from "react";
import {
  Button,
  TextField,
  Typography,
  Box,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormHelperText,
  Checkbox,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import DatePickerModal from "@/presentation/components/common/Modal/DatePickerModal";
import { useAvailabilitySettingState } from "@/presentation/hooks/agenda/settings";
import { ChevronUp } from "lucide-react";

export const Planning = () => {
  const {
    settings,
    settingsLocal,
    handlePlanningTypeChange,
    handleIsMaxDays,
    handleIsMinHours,
    handleMaxDaysChange,
    handleMinHoursChange,
    handleStartDateChange,
    handleEndDateChange,
    errors,
  } = useAvailabilitySettingState();
  const [isStartDatePickerOpen, setIsStartDatePickerOpen] = useState(false);
  const [isEndDatePickerOpen, setIsEndDatePickerOpen] = useState(false);

  return (
    <Accordion>
      <AccordionSummary
        expandIcon={<ChevronUp />}
        aria-controls="planning-period-content"
        id="planning-period-header"
      >
        <Box sx={{ display: "flex", flexDirection: "column" }}>
          <Typography variant="subtitle1">Période de planification</Typography>
          <Typography component="span" sx={{ color: "text.secondary" }}>
            De {settingsLocal.maxDays} jours a l'avance a{" "}
            {settingsLocal.minHours} heures avant
          </Typography>
        </Box>
      </AccordionSummary>
      <AccordionDetails>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Limiter la période pendant laquelle les rendez-vous peuvent être
          réservés
        </Typography>
        <FormControl component="fieldset" sx={{ mb: 2 }}>
          <RadioGroup
            value={settingsLocal.planningType}
            onChange={(e) => handlePlanningTypeChange(e.target.value)}
          >
            <FormControlLabel
              value="now"
              checked={settingsLocal.planningType == "now"}
              control={<Radio size="small" />}
              label="Disponible maintenant"
            />
            <FormControlLabel
              value="date"
              checked={settingsLocal.planningType == "date"}
              control={<Radio size="small" />}
              label="Dates de début et de fin"
            />
          </RadioGroup>
        </FormControl>

        {settingsLocal.planningType == "now" ? (
          <Box sx={{ mb: 2 }}>
            <Box sx={{ m: 1 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Période maximale pendant laquelle un rendez-vous peut être
                réservé
              </Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
              <Checkbox
                size="small"
                checked={settingsLocal.isMaxDays}
                onChange={(e) => handleIsMaxDays(e.target.checked)}
              />
              <TextField
                disabled={!settingsLocal.isMaxDays}
                type="number"
                value={settingsLocal.maxDays}
                onChange={(e) => handleMaxDaysChange(e.target.value)}
                size="small"
                sx={{ width: 100 }}
                error={!!errors.maxDays}
              />
              <Typography>jours</Typography>
            </Box>
            {errors.maxDays && (
              <FormHelperText error>{errors.maxDays}</FormHelperText>
            )}
            <Box sx={{ m: 1 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Période minimale pendant laquelle un rendez-vous peut être
                réservé avant son heure de début
              </Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
              <Checkbox
                size="small"
                checked={settingsLocal.isMinHours}
                onChange={(e) => handleIsMinHours(e.target.checked)}
              />
              <TextField
                disabled={!settingsLocal.isMinHours}
                type="number"
                value={settingsLocal.minHours}
                onChange={(e) => handleMinHoursChange(e.target.value)}
                size="small"
                sx={{ width: 100 }}
                error={!!errors.minHours}
              />
              <Typography>heures</Typography>
            </Box>
            {errors.minHours && (
              <FormHelperText error>{errors.minHours}</FormHelperText>
            )}
          </Box>
        ) : (
          <Box sx={{ mb: 2 }}>
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Période de disponibilité
              </Typography>
              <Box sx={{ display: "flex", gap: 2, mt: 1 }}>
                <Button
                  variant="outlined"
                  onClick={() => setIsStartDatePickerOpen(true)}
                  sx={{ textTransform: "none" }}
                  // error={!!errors.startDate}
                >
                  {settings.date_debut
                    ? new Date(settings.date_debut).toLocaleDateString()
                    : "Date de début"}
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => setIsEndDatePickerOpen(true)}
                  sx={{ textTransform: "none" }}
                  // error={!!errors.endDate}
                >
                  {settings.date_fin
                    ? new Date(settings.date_fin).toLocaleDateString()
                    : "Date de fin"}
                </Button>
              </Box>
              {errors.startDate && (
                <FormHelperText error>{errors.startDate}</FormHelperText>
              )}
              {errors.endDate && (
                <FormHelperText error>{errors.endDate}</FormHelperText>
              )}
            </Box>

            <DatePickerModal
              open={isStartDatePickerOpen}
              onClose={() => setIsStartDatePickerOpen(false)}
              onDateSelect={handleStartDateChange}
            />
            <DatePickerModal
              open={isEndDatePickerOpen}
              onClose={() => setIsEndDatePickerOpen(false)}
              onDateSelect={handleEndDateChange}
            />
          </Box>
        )}
      </AccordionDetails>
    </Accordion>
  );
};

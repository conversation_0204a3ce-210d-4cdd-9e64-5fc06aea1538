import {
  But<PERSON>,
  TextField,
  MenuItem,
  Typography,
  Box,
  Link,
} from "@mui/material";
import { useAvailabilitySettingState } from "@/presentation/hooks/agenda/settings";
import RenderSpecificSchedule from "./renderAvailability/RenderSpecificSchedule";
import RenderWeeklySelector from "./renderAvailability/RenderWeeklySelector";

export const Availability = () => {
  const { settings, errors, handleAvailabilityChange } =
    useAvailabilitySettingState();
  // Render
  const renderAvailability = () => {
    switch (settings.type) {
      case "specifique":
        return <RenderSpecificSchedule />;
      case "hebdomadaire":
        return <RenderWeeklySelector />;
      default:
        return null;
    }
  };

  return (
    <Box sx={{ mb: 4 }}>
      <Typography variant="subtitle1" gutterBottom>
        Disponibilité habituelle
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        Définissez vos disponibilités pour les rendez-vous.{" "}
        <Link href="#" underline="hover">
          En savoir plus
        </Link>
      </Typography>

      <TextField
        select
        value={settings.type}
        onChange={(e) => handleAvailabilityChange(e.target.value)}
        size="small"
        sx={{ mb: 2 }}
      >
        <MenuItem value="specifique">Une seule fois</MenuItem>
        <MenuItem value="hebdomadaire">Toutes les semaines</MenuItem>
      </TextField>
      {renderAvailability()}
    </Box>
  );
};

import { Text<PERSON>ield, Typo<PERSON>, <PERSON>, <PERSON>con<PERSON><PERSON><PERSON>, But<PERSON> } from "@mui/material";
import { CirclePlus, CircleX, Copy, Calendar } from "lucide-react";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { useState } from "react";
import DatePickerModal from "@/presentation/components/common/Modal/DatePickerModal";
import { useAvailabilitySettingState } from "@/presentation/hooks/agenda/settings";

const RenderSpecificSchedule = () => {
  const {
    settings,
    settingsLocal,
    errors,
    handleDateStartTimeChange,
    handleDateEndTimeChange,
    handleAddDateTimeSlot,
    handleDeleteDateTimeSlot,
    handleAddDate,
  } = useAvailabilitySettingState();
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  const handleOpenDatePicker = () => {
    setIsDatePickerOpen(true);
  };

  const handleCloseDatePicker = () => {
    setIsDatePickerOpen(false);
  };

  const handleDateSelect = (date: Date) => {
    handleAddDate(date);
  };

  return (
    <Box>
      {settings.horaire_date_specifique &&
        settings.horaire_date_specifique.map((schedule, dateIndex) => (
          <Box key={dateIndex} sx={{ mb: 3 }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 1 }}>
              <Typography>
                {schedule.date instanceof Date
                  ? format(schedule.date, "dd MMM. yyyy", { locale: fr })
                  : format(new Date(schedule.date), "dd MMM. yyyy", {
                      locale: fr,
                    })}
              </Typography>
            </Box>

            {schedule.creneau_horaire.map((timeSlot, timeSlotIndex) => (
              <Box
                key={timeSlotIndex}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  ml: 2,
                  mb: 1,
                }}
              >
                <TextField
                  type="time"
                  value={timeSlot.heure_debut}
                  onChange={(e) =>
                    handleDateStartTimeChange(
                      dateIndex,
                      timeSlotIndex,
                      e.target.value
                    )
                  }
                  size="small"
                  sx={{ width: 120 }}
                />
                <Typography>-</Typography>
                <TextField
                  type="time"
                  value={timeSlot.heure_fin}
                  onChange={(e) =>
                    handleDateEndTimeChange(
                      dateIndex,
                      timeSlotIndex,
                      e.target.value
                    )
                  }
                  size="small"
                  sx={{ width: 120 }}
                />
                <Box>
                  <IconButton
                    onClick={() =>
                      handleDeleteDateTimeSlot(dateIndex, timeSlotIndex)
                    }
                    size="small"
                    sx={{ "&:hover": { backgroundColor: "transparent" } }}
                  >
                    <CircleX />
                  </IconButton>
                  {timeSlotIndex === 0 && (
                    <>
                      <IconButton
                        onClick={() => handleAddDateTimeSlot(dateIndex)}
                        size="small"
                        sx={{ "&:hover": { backgroundColor: "transparent" } }}
                      >
                        <CirclePlus />
                      </IconButton>
                    </>
                  )}
                </Box>
              </Box>
            ))}
          </Box>
        ))}

      <Button
        startIcon={<Calendar />}
        onClick={handleOpenDatePicker}
        variant="outlined"
        sx={{ mt: 2, textTransform: "none" }}
      >
        Ajouter une date
      </Button>

      <DatePickerModal
        open={isDatePickerOpen}
        onClose={handleCloseDatePicker}
        onDateSelect={handleDateSelect}
      />
    </Box>
  );
};

export default RenderSpecificSchedule;

import { useAvailabilitySettingState } from "@/presentation/hooks/agenda/settings";
import { TextField, Typography, Box } from "@mui/material";

const Duration = () => {
  const { settings, errors, handleDurationChange } =
    useAvailabilitySettingState();
  return (
    <Box sx={{ mb: 4 }}>
      <Typography variant="subtitle1" gutterBottom>
        Durée des rendez-vous
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        Combien de temps chaque rendez-vous doit-il durer ?
      </Typography>
      <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
        <TextField
          type="number"
          value={settings.temps_moyen_consulation}
          onChange={(e) => handleDurationChange(e.target.value)}
          size="small"
          error={!!errors.temps_moyen_consulation}
          helperText={errors.temps_moyen_consulation}
          sx={{ width: 100 }}
        />
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Minutes
        </Typography>
      </Box>
    </Box>
  );
};

export default Duration;

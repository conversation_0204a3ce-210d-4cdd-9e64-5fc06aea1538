import {
  TextField,
  MenuItem,
  Typography,
  Box,
  FormControl,
  FormHelperText,
  Checkbox,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import { useAvailabilitySettingState } from "@/presentation/hooks/agenda/settings";
import { ChevronUp } from "lucide-react";

export const SettingRDV = () => {
  const {
    settings,
    settingsLocal,
    handleDureePauseChange,
    handleIsBreak,
    handleBreakDurationChange,
    handleMaxReservationsChange,
    handleIsMaxReservationsPerDay,
    handleCanInviteOthersChange,
    errors,
  } = useAvailabilitySettingState();
  return (
    <Accordion>
      <AccordionSummary
        expandIcon={<ChevronUp />}
        aria-controls="planning-period-content"
        id="planning-period-header"
      >
        <Box sx={{ display: "flex", flexDirection: "column" }}>
          <Typography variant="subtitle1">
            Paramètres des rendez-vous réservés
          </Typography>
          <Typography component="span" sx={{ color: "text.secondary" }}>
            Gérer les rendez-vous réservés qui apparaîtront dans votre agenda
          </Typography>
        </Box>
      </AccordionSummary>

      <AccordionDetails>
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: "flex", flexDirection: "column" }}>
            <Typography variant="subtitle1">Pause</Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Indiquer le délai entre deux créneaux horaires
            </Typography>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
            <Checkbox
              size="small"
              checked={settingsLocal.isBreak}
              onChange={() => handleIsBreak(!settingsLocal.isBreak)}
            />
            <TextField
              disabled={!settingsLocal.isBreak}
              type="number"
              value={settings.duree_pause}
              onChange={(e) => handleDureePauseChange(e.target.value)}
              size="small"
              sx={{ width: 100 }}
              error={!!errors.duree_pause}
            />
            <FormControl>
              <TextField
                disabled={!settingsLocal.isBreak}
                select
                value={settingsLocal.tempPause}
                onChange={(e) => handleBreakDurationChange(e.target.value)}
                variant="outlined"
                size="small"
              >
                <MenuItem value="minutes">minutes</MenuItem>
                <MenuItem value="heures">heures</MenuItem>
              </TextField>
            </FormControl>
          </Box>
          {errors.breakDuration && (
            <FormHelperText error>{errors.breakDuration}</FormHelperText>
          )}
          <Box sx={{ display: "flex", flexDirection: "column" }}>
            <Typography variant="subtitle1">
              Nombre maximum de réservations par jour
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Limiter le nombre de rendez-vous réservés à accepter par jour
            </Typography>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
            <Checkbox
              size="small"
              checked={settingsLocal.isMaxReservationsPerDay}
              onChange={() =>
                handleIsMaxReservationsPerDay(
                  !settingsLocal.isMaxReservationsPerDay
                )
              }
            />
            <TextField
              disabled={!settingsLocal.isMaxReservationsPerDay}
              type="number"
              value={settings.max_rdv_par_jours}
              onChange={(e) => handleMaxReservationsChange(e.target.value)}
              size="small"
              sx={{ width: 100 }}
              error={!!errors.maxReservationsPerDay}
            />
          </Box>
          {errors.maxReservationsPerDay && (
            <FormHelperText error>
              {errors.maxReservationsPerDay}
            </FormHelperText>
          )}
          <Typography variant="subtitle1">Autorisations des invités</Typography>
          <Box sx={{ display: "flex", gap: 1, mb: 1 }}>
            <Checkbox
              size="small"
              checked={settings.peut_inviter_autre}
              onChange={() =>
                handleCanInviteOthersChange(!settings.peut_inviter_autre)
              }
            />
            <Box sx={{ display: "flex", flexDirection: "column" }}>
              <Typography variant="subtitle2">
                Les invités peuvent inviter d'autres personnes
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Après avoir pris rendez-vous, les invités peuvent modifier
                l’événement d’agenda pour inviter d’autres personnes
              </Typography>
            </Box>
          </Box>
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};

import { useState } from "react";
import {
  Typography,
  TextField,
  Checkbox,
  FormControlLabel,
  Grid,
  Box,
  Button,
  MenuItem,
} from "@mui/material";
import { useEventState } from "@/presentation/hooks/agenda/events";
import DatePickerModal from "@/presentation/components/common/Modal/DatePickerModal";

const AddEvents = () => {
  const [isStartDatePickerOpen, setIsStartDatePickerOpen] = useState(false);
  const [isEndDatePickerOpen, setIsEndDatePickerOpen] = useState(false);
  const {
    title,
    description,
    date_debut,
    date_fin,
    est_toute_la_journee,
    heure_debut,
    heure_fin,
    repetition,
    handleDateDebutChange,
    handleDateFinChange,
    handleIsAllDayChange,
    handleTitleChange,
    handleDescriptionChange,
    handleStartTimeChange,
    handleEndTimeChange,
    handleRepetitionChange,
  } = useEventState();

  return (
    <>
      <Grid item xs={12} sm={6}>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Combien de temps cette evenement durera t-il ?
        </Typography>
      </Grid>
      <Grid item xs={12} sm={6}>
        <Typography>Titre</Typography>
        <TextField
          fullWidth
          value={title}
          onChange={(e) => handleTitleChange(e.target.value)}
          variant="outlined"
          margin="normal"
          placeholder="Titre"
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <Typography>Description</Typography>
        <TextField
          fullWidth
          value={description}
          onChange={(e) => handleDescriptionChange(e.target.value)}
          variant="outlined"
          margin="normal"
          placeholder="Description"
        />
      </Grid>
      <Box className="flex items-center gap-2 my-2 h-10">
        {est_toute_la_journee === true ? (
          <>
            <Button
              variant="outlined"
              onClick={() => setIsStartDatePickerOpen(true)}
              sx={{ textTransform: "none" }}
              // error={!!errors.startDate}
            >
              {date_debut
                ? new Date(date_debut).toLocaleDateString()
                : "Date de début"}
            </Button>
            <Typography>-</Typography>
            <Button
              variant="outlined"
              onClick={() => setIsEndDatePickerOpen(true)}
              sx={{ textTransform: "none" }}
              // error={!!errors.endDate}
            >
              {date_fin
                ? new Date(date_fin).toLocaleDateString()
                : "Date de fin"}
            </Button>
          </>
        ) : (
          <>
            <Button
              variant="outlined"
              onClick={() => setIsStartDatePickerOpen(true)}
              sx={{ textTransform: "none" }}
              // error={!!errors.startDate}
            >
              {date_debut
                ? new Date(date_debut).toLocaleDateString()
                : "Date de début"}
            </Button>
            <TextField
              type="time"
              value={heure_debut}
              onChange={(e) => handleStartTimeChange(e.target.value)}
              size="small"
            />
            <Typography>-</Typography>
            <TextField
              type="time"
              value={heure_fin}
              onChange={(e) => handleEndTimeChange(e.target.value)}
              size="small"
            />
          </>
        )}
      </Box>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Checkbox
              checked={est_toute_la_journee}
              onChange={(e) => handleIsAllDayChange(e.target.checked)}
              size="small"
            />
          }
          label="Toute la journée"
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          select
          size="small"
          sx={{ my: 1 }}
          value={repetition}
          onChange={(e) => handleRepetitionChange(e.target.value)}
        >
          <MenuItem value="once">Une seule fois</MenuItem>
          <MenuItem value="allDays">Toutes les jours</MenuItem>
          <MenuItem value="allWeeks">Toutes les semaines</MenuItem>
          <MenuItem value="weekly">
            Tout les jours de la semaine (du lundi au vendredi)
          </MenuItem>
          <MenuItem value="allMonths">Tous les mois</MenuItem>
          <MenuItem value="allYears">Tous les ans</MenuItem>
        </TextField>
      </Grid>
      <DatePickerModal
        open={isStartDatePickerOpen}
        onClose={() => setIsStartDatePickerOpen(false)}
        onDateSelect={handleDateDebutChange}
      />
      <DatePickerModal
        open={isEndDatePickerOpen}
        onClose={() => setIsEndDatePickerOpen(false)}
        onDateSelect={handleDateFinChange}
      />
    </>
  );
};

export default AddEvents;

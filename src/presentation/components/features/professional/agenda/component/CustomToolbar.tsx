import React from "react";
import {
  Box,
  Button,
  Card,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  styled,
  Typography,
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import { ToolbarProps, NavigateAction, View } from "react-big-calendar";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { PlusDropdown } from "@/presentation/components/layouts/PlusDropdown";
import FilterEvents from "./FilterEvents";
import { getFormattedLabel } from "@/shared/utils/getFormattedLabel";
import { useAgendaState } from "@/presentation/hooks/agenda";

const StyledCard = styled(Card)(({ theme }) => ({
  boxShadow: "none",
  borderRadius: theme.spacing(0.5),
}));

const CustomToolbar: React.FC<ToolbarProps> = (props) => {
  const { currentView, handleViewChange } = useAgendaState();

  const navigate = (action: NavigateAction) => {
    props.onNavigate(action);
  };

  const handleChange = (event: React.ChangeEvent<View>) => {
    handleViewChange(event.target.value);
  };

  return (
    // supprimer le shadow par défault
    <StyledCard className="sticky top-0 z-50">
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "5px",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Button
            onClick={() => navigate("TODAY")}
            variant="outlined"
            size="small"
            sx={{
              padding: "6px",
              borderRadius: 20,
              textTransform: "capitalize",
            }}
          >
            Aujourd'hui
          </Button>
          <IconButton aria-label="ChevronLeft" onClick={() => navigate("PREV")}>
            <ChevronLeft />
          </IconButton>
          <IconButton
            aria-label="ChevronRight"
            onClick={() => navigate("NEXT")}
          >
            <ChevronRight />
          </IconButton>
        </Box>
        <Typography variant="h6">
          {getFormattedLabel(props.date, currentView)}
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Box className="shadow-md rounded-full">
            <PlusDropdown />
          </Box>
          <FormControl sx={{ minWidth: 120 }} size="small">
            <InputLabel id="view-select-label">Vue</InputLabel>
            <Select
              labelId="view-select-label"
              id="view-select"
              value={currentView}
              label="Vue"
              onChange={handleChange}
            >
              <MenuItem value="month">Mois</MenuItem>
              <MenuItem value="week">Semaine</MenuItem>
              <MenuItem value="day">Jour</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>
      <FilterEvents />
    </StyledCard>
  );
};

export default CustomToolbar;

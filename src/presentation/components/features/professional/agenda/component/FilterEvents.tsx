import { Box, Checkbox, FormControlLabel, Typography } from "@mui/material";
import {
  DESTRUCTIVE,
  EVENT_COLOR,
  GREEN,
  PRIMARY,
} from "@/shared/constants/Color";
import React from "react";
import { useAgendaState } from "@/presentation/hooks/agenda";

const FilterEvents = () => {
  const {
    isDisponibilites,
    isEvenement,
    isAppointments,
    handleIsEvenementChange,
    handleIsDisponibilitesChange,
    handleIsAppointmentsChange,
  } = useAgendaState();
  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        padding: "5px",
        gap: 2,
        ml: 2,
      }}
    >
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <FormControlLabel
          control={
            <Checkbox
              size="small"
              sx={{
                color: EVENT_COLOR,
                "&.Mui-checked": {
                  color: EVENT_COLOR,
                },
              }}
              checked={isEvenement}
              onChange={(e) => handleIsEvenementChange(e.target.checked)}
            />
          }
          label={
            <Typography sx={{ color: EVENT_COLOR }}>évenements</Typography>
          }
        />
      </Box>
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <FormControlLabel
          control={
            <Checkbox
              size="small"
              sx={{
                color: PRIMARY,
                "&.Mui-checked": {
                  color: PRIMARY,
                },
              }}
              checked={isDisponibilites}
              onChange={(e) => handleIsDisponibilitesChange(e.target.checked)}
            />
          }
          label={
            <Typography sx={{ color: PRIMARY }}>disponibiliteés</Typography>
          }
        />
      </Box>
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <FormControlLabel
          control={
            <Checkbox
              size="small"
              sx={{
                color: GREEN,
                "&.Mui-checked": {
                  color: GREEN,
                },
              }}
              checked={isAppointments}
              onChange={(e) => handleIsAppointmentsChange(e.target.checked)}
            />
          }
          label={<Typography sx={{ color: GREEN }}>rendez-vous</Typography>}
        />
      </Box>
    </Box>
  );
};

export default FilterEvents;

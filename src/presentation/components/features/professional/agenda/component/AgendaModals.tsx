import AddEventModal from "@/presentation/components/common/Modal/AddEventModal";
import { AddSettingModal } from "@/presentation/components/common/Modal/AddSettingModal";
import { BookAppointmentModal } from "@/presentation/components/common/Modal/BookAppointmentModal";
import DeleteEventModal from "@/presentation/components/common/Modal/DeleteEventModal";
import DeleteSettingsModal from "@/presentation/components/common/Modal/DeleteSettingsModal";
import { useAgendaState } from "@/presentation/hooks/agenda";

export const AgendaModals = () => {
  const {
    isSettingsModalOpen,
    isAddEventModalOpen,
    isDeleteEventModalOpen,
    isDeleteSettingsModalOpen,
    isAppointmentModalOpen,
  } = useAgendaState();
  return (
    <>
      {isSettingsModalOpen && <AddSettingModal />}

      {isAddEventModalOpen && <AddEventModal />}

      {isDeleteEventModalOpen && <DeleteEventModal />}

      {isDeleteSettingsModalOpen && <DeleteSettingsModal />}

      {isAppointmentModalOpen && <BookAppointmentModal />}
    </>
  );
};

import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { <PERSON><PERSON>, Chip } from "@mui/material";
import { statusColors } from "@/shared/constants/statusColors";
import { AppointmentPatientDTO } from "@/domain/DTOS/AppointmentPatientDTO";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { EventBusy as EventBusyIcon } from "@mui/icons-material";

export const AppointmentColumnsPatient = (): GridColDef[] => {
  const { handleCancelAppointment } = useConsultationState();
  return [
    {
      field: "date_rendez_vous",
      headerName: "Date",
      width: 160,
      valueFormatter: (params) => new Date(params).toLocaleString(),
      headerClassName: "font-semibold",
    },
    {
      field: "Docteur",
      headerName: "docteur",
      width: 200,
      renderCell: (params: GridRenderCellParams<AppointmentPatientDTO>) =>
        `${params.row.professional.nom} ${params.row.professional.prenom}`,
      headerClassName: "font-semibold",
    },
    {
      field: "motif",
      headerName: "Motif",
      width: 250,
      headerClassName: "font-semibold",
    },
    {
      field: "statut",
      headerName: "Statut",
      width: 150,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={statusColors[params.value as keyof typeof statusColors]}
          size="small"
        />
      ),
      headerClassName: "font-semibold",
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 120,
      renderCell: (params) => (
        <Button
          onClick={() => handleCancelAppointment(params.row.id)}
          variant="contained"
          color="error"
          size="small"
          sx={{ textTransform: "none", gap: 1 }}
          disabled={
            params.row.statut !== rendez_vous_statut_enum.A_VENIR &&
            params.row.statut !== rendez_vous_statut_enum.REPORTER
          }
        >
          <EventBusyIcon sx={{ width: 20, height: 20 }} />
          Annuler
        </Button>
      ),
      headerClassName: "font-semibold",
    },
  ];
};

import { useCarnetDeSanteData } from "@/presentation/hooks/carnetDeSante";
import { format } from "date-fns";
import { fr } from "date-fns/locale";

const SigneVitauxSection = () => {
  const { signeVitaux } = useCarnetDeSanteData();
  return (
    signeVitaux.length > 0 && (
      <>
        <p>
          <span className="dark:text-white text-black">Taille : </span>
          <span className="dark:text-gray-400 text-gray-600">
            {signeVitaux[signeVitaux.length - 1]?.taille} cm
          </span>
        </p>
        <p>
          <span className="dark:text-white text-black">Poids : </span>
          <span className="dark:text-gray-400 text-gray-600">
            {signeVitaux[signeVitaux.length - 1]?.poid} kg
          </span>
        </p>
        <p>
          <span className="dark:text-white text-black">Date de visite : </span>
          <span className="dark:text-gray-400 text-gray-600">
            {format(
              new Date(signeVitaux[signeVitaux.length - 1]?.date_visite),
              "EEEE d MMMM",
              { locale: fr }
            )}
          </span>
        </p>
      </>
    )
  );
};

export default SigneVitauxSection;

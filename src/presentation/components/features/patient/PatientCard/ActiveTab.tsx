import ToggleButton from "@mui/material/ToggleButton";
import ToggleButtonGroup from "@mui/material/ToggleButtonGroup";
import { active_tab_enum } from "@/domain/models/enums";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";

const navButton = [
  {
    value: active_tab_enum.carnetDeSante,
    text: "Carnet de santé",
  },
  {
    value: active_tab_enum.consultationMedicale,
    text: "Consultation médicale",
  },
  {
    value: active_tab_enum.signeVitaux,
    text: "Signe vitaux",
  },
  {
    value: active_tab_enum.facturation,
    text: "Facturation",
  },
];

const ActiveTab = () => {
  const {
    activeTab,
    isProfile,
    handleActiveTabChange,
    setIsProfile,
    setIsAddForm,
  } = useCarnetDeSanteState();
  return (
    <div className="mx-auto pb-1">
      {!isProfile && (
        <ToggleButtonGroup
          color="primary"
          value={activeTab}
          exclusive
          fullWidth
          aria-label="Platform"
        >
          {navButton.map((nav) => (
            <ToggleButton
              key={nav.value}
              value={nav.value}
              sx={{ textTransform: "none" }}
              onClick={() => {
                handleActiveTabChange(nav.value);
                setIsProfile(false);
                setIsAddForm(false);
              }}
              size="small"
            >
              {nav.text}
            </ToggleButton>
          ))}
        </ToggleButtonGroup>
      )}
    </div>
  );
};

export default ActiveTab;

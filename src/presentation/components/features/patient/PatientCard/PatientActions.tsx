import React from "react";
import { But<PERSON> } from "@mui/material";
import { PRIMARY } from "@/shared/constants/Color";
import { UserIcon, X, SkullIcon } from "lucide-react";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";

interface PatientActionsProps {
  setIsDeletePatientModal: (val: boolean) => void;
  setIsPatientDecedeModal: (val: boolean) => void;
  isPatientDecede: boolean;
}

const PatientActions = ({
  setIsDeletePatientModal,
  setIsPatientDecedeModal,
  isPatientDecede,
}: PatientActionsProps) => {
  const { setIsProfile } = useCarnetDeSanteState();
  return (
    <div className="hidden lg:block lg:mt-8">
      {/* se met a la ligne automatiquement */}
      <div className="flex gap-2 flex-wrap">
        <Button
          variant="contained"
          onClick={() => setIsProfile(true)}
          sx={{
            textTransform: "none",
            backgroundColor: PRIMARY,
            borderRadius: 5,
          }}
          startIcon={<UserIcon size={20} />}
        >
          Profile
        </Button>
        <Button
          variant="contained"
          onClick={() => setIsDeletePatientModal(true)}
          sx={{
            textTransform: "none",
            backgroundColor: PRIMARY,
            borderRadius: 5,
          }}
          startIcon={<X size={20} />}
        >
          Retirer
        </Button>
        {!isPatientDecede && (
          <Button
            variant="contained"
            onClick={() => setIsPatientDecedeModal(true)}
            sx={{
              textTransform: "none",
              backgroundColor: PRIMARY,
              borderRadius: 5,
            }}
            startIcon={<SkullIcon size={20} />}
          >
            Décede
          </Button>
        )}
      </div>
    </div>
  );
};

export default PatientActions;

import {
  Typography,
  Radio,
  RadioGroup,
  FormControlLabel,
  Paper,
} from "@mui/material";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { categorie_enum } from "@/domain/models/enums";
import useProfessionals from "@/presentation/hooks/use-professionals";

const ConsultationCategorie = () => {
  const { categorie, handleCategorieChange } = useConsultationState();
  const { selectedProfessionnal } = useProfessionals();

  return (
    <Paper sx={{ marginBottom: 3, p: 2, border: "1px solid #ccc" }}>
      <Typography
        variant="h6"
        color="green"
        gutterBottom
        sx={{ fontWeight: "semi-bold" }}
      >
        Première consultation?
      </Typography>

      <RadioGroup
        value={categorie}
        onChange={(e) => handleCategorieChange(e.target.value)}
      >
        <FormControlLabel
          value={categorie_enum["premiere consultation"]}
          control={<Radio />}
          label={`C'est ma première consultation avec le Dr. ${selectedProfessionnal?.nom}`}
        />
        <FormControlLabel
          value={categorie_enum["consultation de suivi"]}
          control={<Radio />}
          label={`Je suis déja suivi(e) par le Dr. ${selectedProfessionnal?.nom}`}
        />
      </RadioGroup>
    </Paper>
  );
};

export default ConsultationCategorie;

import { Typography, Box, Select, MenuItem, Paper } from "@mui/material";
import { StyledFormControl } from "@/styles/styleMui/ConsultationSteper";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";

const ConsultationMotif = () => {
  const { categorie, consultationMotif, handleConsultationMotifChange } =
    useConsultationState();

  return (
    <>
      <Box sx={{ width: "100%", maxWidth: 800, margin: "0 auto" }}>
        <Paper sx={{ marginBottom: 3, p: 2, border: "1px solid #ccc" }}>
          <Typography variant="h6" color="green" gutterBottom>
            Motif de consultation
          </Typography>
          <StyledFormControl>
            <Select
              value={consultationMotif}
              onChange={(e) => handleConsultationMotifChange(e.target.value)}
              displayEmpty
              renderValue={
                consultationMotif !== ""
                  ? undefined
                  : () => "Sélectionnez un motif de consultation"
              }
            >
              {categorie === "Première consultation" ? (
                <MenuItem value="premiere-consultation">
                  Première consultation
                </MenuItem>
              ) : (
                <MenuItem value="suivi">Consultation de suivi</MenuItem>
              )}
              <MenuItem value="urgence">Consultation d'urgence</MenuItem>
            </Select>
          </StyledFormControl>
        </Paper>
      </Box>
    </>
  );
};

export default ConsultationMotif;

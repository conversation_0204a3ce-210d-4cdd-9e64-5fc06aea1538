import { Typography, Box, Select, MenuItem, Paper } from "@mui/material";
import { StyledFormControl } from "@/styles/styleMui/ConsultationSteper";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import useProfessionals from "@/presentation/hooks/use-professionals";

const ConsultationSpeciality = () => {
  const { selectedProfessionnal } = useProfessionals();
  const { speciality, handleSpecialityChange } = useConsultationState();

  return (
    <>
      <Box sx={{ width: "100%", maxWidth: 800, margin: "0 auto" }}>
        <Paper sx={{ marginBottom: 3, p: 2, border: "1px solid #ccc" }}>
          <Typography
            variant="h6"
            color="green"
            gutterBottom
            sx={{ fontWeight: "semi-bold" }}
          >
            Spécialités
          </Typography>
          <StyledFormControl>
            <Select
              value={speciality}
              onChange={(e) => handleSpecialityChange(e.target.value)}
              displayEmpty
              renderValue={
                speciality !== ""
                  ? undefined
                  : () => "Sélectionnez une spécialité"
              }
            >
              {selectedProfessionnal?.specialites_professionnel &&
                selectedProfessionnal?.specialites_professionnel.length &&
                selectedProfessionnal?.specialites_professionnel.map((spec) => {
                  return (
                    <MenuItem value={spec.nom_specialite} key={spec.id}>
                      {spec.nom_specialite}
                    </MenuItem>
                  );
                })}
            </Select>
          </StyledFormControl>
        </Paper>
      </Box>
    </>
  );
};

export default ConsultationSpeciality;

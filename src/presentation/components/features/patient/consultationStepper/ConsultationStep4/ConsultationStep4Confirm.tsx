import { Paper } from "@mui/material";
import { StyledButton } from "@/styles/styleMui/ConsultationSteper";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";

const ConsultationStep4Confirm = () => {
  const { forWhom, isPageValid, activeStep, handleCreateAppointment } =
    useConsultationState();

  return (
    <Paper sx={{ marginBottom: 3, p: 2 }}>
      <StyledButton
        variant="contained"
        className="w-full"
        disabled={!forWhom || !isPageValid}
        onClick={() => handleCreateAppointment(activeStep + 1)}
      >
        confirmer rendez-vous
      </StyledButton>
    </Paper>
  );
};

export default ConsultationStep4Confirm;

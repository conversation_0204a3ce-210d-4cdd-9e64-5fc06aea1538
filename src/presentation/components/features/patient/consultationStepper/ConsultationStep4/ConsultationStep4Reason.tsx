import { Typography, TextField, Paper } from "@mui/material";
import { FormSection } from "@/styles/styleMui/ConsultationSteper";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";

const ConsultationStep4Reason = () => {
  const { consultationReason, handleConsultationReasonChange } =
    useConsultationState();

  return (
    <Paper sx={{ marginBottom: 3, p: 2 }}>
      <FormSection>
        <Typography variant="h6" color="green" gutterBottom>
          Raison de la visite / Renseignements complémentaires
        </Typography>
        <TextField
          fullWidth
          multiline
          rows={2}
          placeholder="Transmettre un message au personnel soignant (optionnel)"
          value={consultationReason}
          onChange={(e) => handleConsultationReasonChange(e.target.value)}
        />
      </FormSection>
    </Paper>
  );
};

export default ConsultationStep4Reason;

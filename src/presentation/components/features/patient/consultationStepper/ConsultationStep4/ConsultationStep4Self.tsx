import {
  Box,
  Typography,
  TextField,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  Paper,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { Edit as EditIcon } from "@mui/icons-material";
import { EditButton, FormSection } from "@/styles/styleMui/ConsultationSteper";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { fr } from "date-fns/locale";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { sexe_enum } from "@/domain/models/enums";
import { useEffect } from "react";

const ConsultationStep4Self = () => {
  const { userInformations } = useConsultationState();

  if (!userInformations) {
    // toast.error("hello");
    return;
  }

  return (
    <Paper sx={{ marginBottom: 3, p: 2 }}>
      <FormSection>
        <Box display="flex" alignItems="center">
          <Typography variant="h6">À propos de vous</Typography>
        </Box>

        <FormControl fullWidth margin="normal">
          <InputLabel>Sexe*</InputLabel>
          <Select value={userInformations.sexe} label="Sexe*" disabled>
            <MenuItem value={sexe_enum.homme}>Homme</MenuItem>
            <MenuItem value={sexe_enum.femme}>Femme</MenuItem>
          </Select>
        </FormControl>

        <TextField
          fullWidth
          label="Prénom*"
          value={userInformations.nom}
          margin="normal"
          disabled
        />

        <TextField
          fullWidth
          label="Nom de famille*"
          value={userInformations.prenom}
          margin="normal"
          disabled
        />

        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
          <DatePicker
            sx={{ mt: 2, width: "100%" }}
            value={
              userInformations.date_naissance
                ? new Date(userInformations.date_naissance)
                : null
            }
            disabled
          />
        </LocalizationProvider>
      </FormSection>
    </Paper>
  );
};

export default ConsultationStep4Self;

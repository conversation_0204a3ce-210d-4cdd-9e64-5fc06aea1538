import { Box } from "@mui/material";
import { WarningBox } from "@/styles/styleMui/ConsultationSteper";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import ConsultationStep4ForWhom from "./ConsultationStep4ForWhom";
import ConsultationStep4ForOther from "./ConsultationStep4ForOther";
import ConsultationStep4Reason from "./ConsultationStep4Reason";
import ConsultationStep4Self from "./ConsultationStep4Self";
import ConsultationStep4Confirm from "./ConsultationStep4Confirm";
import SumUp from "@/presentation/pages/patient/consultation/SumUp";
import WarningSumUp from "../WarningSumUp";

const ConsultationStep4 = () => {
  const { forWhom } = useConsultationState();

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-12 mt-8">
      <div className="lg:col-span-2">
        <Box sx={{ width: "100%", maxWidth: 800, margin: "0 auto" }}>
          <WarningBox severity="warning">
            Votre RDV n'est pas encore confirmé. Merci de compléter et vérifier
            les informations ci-dessous pour confirmer votre rendez-vous.
          </WarningBox>

          <ConsultationStep4ForWhom />

          {forWhom === "other" && ( // champ pour si c'est pour le proche
            <ConsultationStep4ForOther />
          )}

          {forWhom && (
            <>
              <ConsultationStep4Self />
              <ConsultationStep4Reason />
            </>
          )}

          <ConsultationStep4Confirm />
        </Box>
      </div>
      <SumUp>
        <WarningSumUp />
      </SumUp>
    </div>
  );
};

export default ConsultationStep4;

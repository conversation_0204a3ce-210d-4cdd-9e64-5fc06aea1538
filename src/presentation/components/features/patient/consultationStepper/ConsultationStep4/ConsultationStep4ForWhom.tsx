import {
  Typography,
  Radio,
  RadioGroup,
  FormControlLabel,
  Paper,
} from "@mui/material";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";

const ConsultationStep4ForWhom = () => {
  const { forWhom, handleForWhomChange } = useConsultationState();

  return (
    <Paper sx={{ marginBottom: 3, p: 2 }}>
      <Typography variant="h6" color="primary" gutterBottom>
        Pour qui prenez-vous rendez-vous?
      </Typography>

      <RadioGroup
        value={forWhom}
        onChange={(e) => handleForWhomChange(e.target.value)}
      >
        <FormControlLabel value="self" control={<Radio />} label="Vous-même" />
        <FormControlLabel
          value="other"
          control={<Radio />}
          label="Un proche (enfant, parent, etc.)"
        />
      </RadioGroup>
    </Paper>
  );
};

export default ConsultationStep4ForWhom;

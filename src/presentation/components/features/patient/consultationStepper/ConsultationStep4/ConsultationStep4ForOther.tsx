import {
  Typo<PERSON>,
  TextField,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  Paper,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { FormSection } from "@/styles/styleMui/ConsultationSteper";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { fr } from "date-fns/locale";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { sexe_enum } from "@/domain/models/enums";
import { useEffect } from "react";
import { useAppSelector } from "@/presentation/hooks/redux";

const ConsultationStep4ForOther = () => {
  const {
    procheInfo,
    handleProcheInfoSexChange,
    handleProcheInfoFirstNameChange,
    handleProcheInfoLastNameChange,
    handleProcheInfoBirthDateChange,
    handleProcheInfoChange,
  } = useConsultationState();
  const id_patient = useAppSelector(
    (state) => state.authentification.userData?.id
  );

  useEffect(() => {
    if (!procheInfo) {
      handleProcheInfoChange({
        id_patient: id_patient,
        sexe: null,
        nom: "",
        prenom: "",
        date_naissance: null,
      });
    }
  }, []);

  return (
    <Paper sx={{ marginBottom: 3, p: 2 }}>
      <FormSection>
        <Typography variant="h6" gutterBottom>
          Informations sur le patient
        </Typography>
        <FormControl fullWidth margin="normal">
          <InputLabel>Sexe*</InputLabel>
          <Select
            value={procheInfo?.sexe || ""}
            onChange={(e) =>
              handleProcheInfoSexChange(e.target.value as sexe_enum)
            }
            label="Sexe*"
          >
            <MenuItem value={sexe_enum.homme}>Homme</MenuItem>
            <MenuItem value={sexe_enum.femme}>Femme</MenuItem>
          </Select>
        </FormControl>

        <TextField
          fullWidth
          label="Prénom*"
          value={procheInfo?.prenom || ""}
          onChange={(e) => handleProcheInfoFirstNameChange(e.target.value)}
          margin="normal"
        />

        <TextField
          fullWidth
          label="Nom de famille*"
          value={procheInfo?.nom || ""}
          onChange={(e) => handleProcheInfoLastNameChange(e.target.value)}
          margin="normal"
        />

        <FormControl fullWidth margin="normal">
          <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
            <DatePicker
              label="Date de naissance*"
              sx={{ width: "100%" }}
              value={
                procheInfo?.date_naissance
                  ? new Date(procheInfo.date_naissance)
                  : null
              }
              onChange={(date) => {
                if (date) {
                  const isoDate = date.toISOString();
                  handleProcheInfoBirthDateChange(isoDate);
                }
              }}
              maxDate={new Date()}
              slotProps={{
                textField: {
                  fullWidth: true,
                  margin: "normal",
                },
              }}
            />
          </LocalizationProvider>
        </FormControl>
      </FormSection>
    </Paper>
  );
};

export default ConsultationStep4ForOther;

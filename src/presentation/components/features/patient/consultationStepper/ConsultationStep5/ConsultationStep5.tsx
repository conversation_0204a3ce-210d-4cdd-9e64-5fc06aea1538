import { useNavigate } from "react-router-dom";
import { Box, Typography, Avatar, Paper } from "@mui/material";
import {
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Event as EventIcon,
  Healing as HealingIcon,
  List as ListIcon,
} from "@mui/icons-material";
import {
  ActionButton,
  ConfirmationBox,
  InfoItem,
  StyledBox5,
} from "@/styles/styleMui/ConsultationSteper";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { useLocation } from "react-router-dom";
import useProfessionals from "@/presentation/hooks/use-professionals";

const ConsultationStep5 = () => {
  const location = useLocation();
  const { selectedProfessionnal } = useProfessionals();
  const { consultationMotif } = useConsultationState();
  const searchParams = new URLSearchParams(location.search);
  const date = searchParams.get("date");

  const navigate = useNavigate();

  const handleViewAppointments = () => {
    navigate("/patient/rendez-vous");
  };

  const handleNewAppointment = () => {
    // navigate('/rdv/1')
  };

  return (
    <StyledBox5>
      <ConfirmationBox>
        <Typography variant="h5" color="green" gutterBottom>
          Votre rendez-vous est confirmé!
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Nous venons de vous envoyer un email de confirmation de rendez-vous
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Vous recevrez également un SMS de rappel 24h avant votre consultation!
        </Typography>
      </ConfirmationBox>

      <Paper sx={{ marginBottom: 3, p: 2 }}>
        <Box display="flex" gap={4}>
          <Box flex={1}>
            <Typography variant="h6" gutterBottom>
              À propos de votre rendez-vous
            </Typography>

            <Box display="flex" alignItems="center" mb={2}>
              <Avatar
                src={selectedProfessionnal?.nom}
                alt={selectedProfessionnal?.nom}
                sx={{ width: 56, height: 56, mr: 2 }}
              />
              <Box>
                <Typography variant="h6" component="div">
                  {`${selectedProfessionnal?.titre} ${selectedProfessionnal?.nom} ${selectedProfessionnal?.prenom}`}
                </Typography>
                <Typography color="textSecondary" variant="body2">
                  {selectedProfessionnal?.specialites_professionnel
                    .map((spec) => spec.nom_specialite)
                    .join(", ")}
                </Typography>
              </Box>
            </Box>

            <InfoItem>
              <EventIcon />
              <Typography>
                {format(new Date(date), "EEEE dd MMMM yyyy", {
                  locale: fr,
                })}
              </Typography>
            </InfoItem>

            <InfoItem>
              <HealingIcon />
              <Typography>{consultationMotif}</Typography>
            </InfoItem>

            <Typography variant="h6" sx={{ mt: 4, mb: 2 }}>
              Informations pratiques
            </Typography>

            <InfoItem>
              <LocationIcon />
              {selectedProfessionnal?.etablissements_professionnel.map(
                (etablishment) => {
                  return (
                    <Box>
                      <Typography>{etablishment.nom_etablissement}</Typography>
                      <Typography color="textSecondary">
                        {selectedProfessionnal?.adresse}
                      </Typography>
                      <Typography color="textSecondary">
                        {selectedProfessionnal?.commune}
                      </Typography>
                    </Box>
                  );
                }
              )}
            </InfoItem>

            <InfoItem>
              <PhoneIcon />
              <Typography>{selectedProfessionnal?.numero_ordre}</Typography>
            </InfoItem>

            <Box sx={{ mt: 4, display: "flex", gap: 2 }}>
              <ActionButton
                variant="contained"
                color="primary"
                startIcon={<ListIcon />}
                onClick={handleViewAppointments}
              >
                VOIR MES RENDEZ-VOUS
              </ActionButton>
              <ActionButton
                variant="contained"
                color="primary"
                startIcon={<EventIcon />}
                onClick={handleNewAppointment}
              >
                NOUVEAU RENDEZ-VOUS
              </ActionButton>
            </Box>
          </Box>
        </Box>
      </Paper>
    </StyledBox5>
  );
};

export default ConsultationStep5;

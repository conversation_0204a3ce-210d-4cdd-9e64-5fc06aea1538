import { Box, Typography, Paper, Button } from "@mui/material";
// import { MuiTelInput } from 'mui-tel-input'
import {
  StyledButton,
  WarningBox2,
} from "@/styles/styleMui/ConsultationSteper";
import ConsultationStep2Register from "./ConsultationStep2Register";
import { useEffect, useState } from "react";
import ConsultationStep2Login from "./ConsultationStep2Login";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import SumUp from "@/presentation/pages/patient/consultation/SumUp";
import WarningSumUp from "@/presentation/components/features/patient/consultationStepper/WarningSumUp";
import { ArrowLeft, X } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { useAppSelector } from "@/presentation/hooks/redux";

const ConsultationStep2 = () => {
  const user = useAppSelector((state) => state.authentification?.user);
  const [identification, setIdentification] = useState("");
  const { activeStep, handleresetAuth, handleActiveStepChange } =
    useConsultationState();

  useEffect(() => {
    if (user && user.id) {
      if (identification === "S'inscrire") {
        handleActiveStepChange(activeStep + 1);
      } else {
        handleActiveStepChange(activeStep + 2);
      }
    }
  }, [user, handleActiveStepChange, activeStep]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-12 mt-8">
      <div className="lg:col-span-2">
        <Box sx={{ width: "100%", maxWidth: 800, margin: "0 auto" }}>
          <WarningBox2>
            <Typography variant="body2">
              Votre RDV n'est pas encore confirmé. Merci de vous identifier pour
              confirmer votre rendez-vous.
            </Typography>
          </WarningBox2>
          <Button
            onClick={() => handleActiveStepChange(activeStep - 1)}
            disabled={activeStep == 0}
            variant="contained"
            sx={{
              textTransform: "none",
              marginBottom: 2,
              backgroundColor: PRIMARY,
              gap: 1,
            }}
          >
            <ArrowLeft className="h-4 w-4" /> Precedent
          </Button>
          <Paper sx={{ marginBottom: 3, p: 2 }}>
            <Typography sx={{ marginBottom: 3, textAlign: "center" }}>
              Nouveau sur MEDDoC?
            </Typography>
            {identification === "S'inscrire" ? (
              <>
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  Saisissez vos informations pour continuer.
                </Typography>
                <ConsultationStep2Register />
              </>
            ) : (
              <>
                <StyledButton
                  variant="contained"
                  className="w-full"
                  onClick={() => {
                    setIdentification("S'inscrire");
                    handleresetAuth();
                  }}
                >
                  S'inscrire
                </StyledButton>
              </>
            )}
          </Paper>

          <Paper sx={{ marginBottom: 3, p: 2 }}>
            <Typography sx={{ marginBottom: 3, textAlign: "center" }}>
              Connexion avec votre compte MEDDoC?
            </Typography>
            {identification === "Se connecter" ? (
              <ConsultationStep2Login />
            ) : (
              <>
                <StyledButton
                  variant="contained"
                  className="w-full"
                  onClick={() => {
                    setIdentification("Se connecter");
                    handleresetAuth();
                  }}
                >
                  Se connecter
                </StyledButton>
              </>
            )}
          </Paper>
        </Box>
      </div>
      <SumUp>
        <WarningSumUp />
      </SumUp>
    </div>
  );
};

export default ConsultationStep2;

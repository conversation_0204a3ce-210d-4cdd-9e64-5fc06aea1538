import { Typo<PERSON>, FormControlLabel, Checkbox, Link } from "@mui/material";
// import { MuiTelInput } from 'mui-tel-input'
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { StyledButton } from "@/styles/styleMui/ConsultationSteper";
import RegisterStep1FirstName from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1FirstName";
import RegisterStep1Email from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1Email";
import RegisterStep1Sexe from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1Sexe";
import RegisterStep1Password from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1Password";
import RegisterStep1Birthday from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1Birthday";
import RegisterStep1LastName from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1LastName";
import RegisterStep1confirmEmail from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep1/RegisterStep1confirmEmail";
import RegisterStep2District from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep2/RegisterStep2District";
import RegisterStep2Commune from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep2/RegisterStep2Commune";
import RegisterStep2Adresse from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep2/RegisterStep2Adresse";
import RegisterStep3Telephones from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep3/RegisterStep3Telephones";
import { onSubmitRegisterPatient } from "@/shared/utils/onSubmitRegisterPatient";
import useRegister from "@/presentation/hooks/use-register";
import RegisterStep2Fokontany from "@/presentation/components/features/patient/registerPatienStepper/RegisterStep2/RegisterStep2Fokontany";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";

const ConsultationStep2Register = () => {
  const { state } = useRegisterPatientState();
  const {
    acceptConditions,
    activeStep,
    handleAcceptConditionsChange,
    handleActiveStepChange,
  } = useConsultationState();

  const { error, loading } = useRegister();
  const { registerPatient } = useConsultationState();

  const handleSubmit = async () => {
    await onSubmitRegisterPatient(state, registerPatient);
    if (!error) {
      handleActiveStepChange(activeStep + 1);
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <RegisterStep1FirstName />
        <RegisterStep1LastName />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <RegisterStep1Sexe />
        <RegisterStep1Birthday />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <RegisterStep1Email />
        <RegisterStep1confirmEmail />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <RegisterStep1Password />
        <RegisterStep2Adresse />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <RegisterStep2District />
        <RegisterStep2Commune />
        <RegisterStep2Fokontany />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <RegisterStep3Telephones />
      </div>

      <FormControlLabel
        control={
          <Checkbox
            checked={acceptConditions}
            onChange={(e) => handleAcceptConditionsChange(e.target.checked)}
            color="primary"
          />
        }
        label={
          <Typography variant="body2">
            J'accepte les{" "}
            <Link href="#" color="primary">
              conditions générales d'utilisation
            </Link>{" "}
            de MEDDoC
          </Typography>
        }
        sx={{ mt: 2 }}
      />
      <StyledButton
        variant="contained"
        className="w-full"
        onClick={handleSubmit}
        disabled={!acceptConditions}
        loading={loading}
      >
        Continuer
      </StyledButton>
    </>
  );
};

export default ConsultationStep2Register;

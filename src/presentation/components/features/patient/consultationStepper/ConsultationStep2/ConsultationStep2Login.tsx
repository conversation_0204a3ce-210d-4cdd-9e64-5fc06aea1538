// import { MuiTelInput } from 'mui-tel-input'
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { StyledButton } from "@/styles/styleMui/ConsultationSteper";
import useLogin from "@/presentation/hooks/use-login";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useEffect } from "react";
import EmailLogin from "@/presentation/components/layouts/EmailLogin";
import PasswordLogin from "@/presentation/components/layouts/PasswordLogin";
import { formSchema, UseLoginForm } from "@/presentation/hooks/use-login-form";
import * as z from "zod";

const ConsultationStep2Login = () => {
  const user = useAppSelector((state) => state.authentification.user);
  const { login, loading } = useLogin();
  const { errors, register, handleSubmit } = UseLoginForm();

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    await login(values.email, values.password);
  };

  return (
    <>
      <form
        onSubmit={handleSubmit(onSubmit)}
        style={{ display: "flex", flexDirection: "column" }}
      >
        <EmailLogin errors={errors} register={register} />
        <PasswordLogin errors={errors} register={register} />
        <StyledButton
          type="submit"
          variant="contained"
          className="w-full"
          loading={loading}
        >
          Se connecter
        </StyledButton>
      </form>
    </>
  );
};

export default ConsultationStep2Login;

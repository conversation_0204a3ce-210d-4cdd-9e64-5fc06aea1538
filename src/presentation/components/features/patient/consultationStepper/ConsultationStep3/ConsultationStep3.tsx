import { useState } from "react";
import { Box, TextField, Button, Typography, Paper } from "@mui/material";
import { styled } from "@mui/material/styles";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import useRegister from "@/presentation/hooks/use-register";

const VerificationBox = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  gap: theme.spacing(3),
  maxWidth: 400,
  margin: "0 auto",
  marginTop: theme.spacing(4),
}));

const CodeInputContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  gap: theme.spacing(1),
  justifyContent: "center",
  width: "100%",
}));

const ConsultationStep3 = () => {
  const { activeStep, handleActiveStepChange } = useConsultationState();
  const {
    error,
    verificationCode,
    handleVerificationCode,
    handleVerificationCodeError,
  } = useRegister();

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...verificationCode];
      newCode[index] = value;
      handleVerificationCode(newCode);
      handleVerificationCodeError(null);

      // Auto-focus next input
      if (value && index < 3) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLDivElement>
  ) => {
    if (e.key === "Backspace" && !verificationCode[index] && index > 0) {
      const prevInput = document.getElementById(`code-${index - 1}`);
      prevInput?.focus();
    }
  };

  const handleSubmit = () => {
    const code = verificationCode.join("");
    if (code.length !== 4) {
      handleVerificationCodeError("Veuillez entrer les 4 chiffres du code");
      return;
    }
    // TODO: Add verification logic here
    console.log("Verification code submitted:", code);
    // si correcte
    handleActiveStepChange(activeStep + 1);
  };

  return (
    <VerificationBox elevation={3}>
      <Typography variant="h5" component="h2" gutterBottom>
        Confirmation de votre compte
      </Typography>
      <Typography variant="body1" color="textSecondary" align="center">
        Veuillez entrer le code de vérification à 4 chiffres envoyé à votre
        adresse e-mail
      </Typography>

      <CodeInputContainer>
        {verificationCode.map((digit, index) => (
          <TextField
            key={index}
            id={`code-${index}`}
            value={digit}
            onChange={(e) => handleCodeChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            variant="outlined"
            inputProps={{
              maxLength: 1,
              style: { textAlign: "center", fontSize: "1.5rem" },
            }}
            sx={{ width: "60px", height: "60px" }}
          />
        ))}
      </CodeInputContainer>

      {error && (
        <Typography color="error" variant="body2">
          {error}
        </Typography>
      )}

      <Button
        variant="contained"
        color="primary"
        fullWidth
        onClick={handleSubmit}
        size="large"
      >
        Vérifier
      </Button>
    </VerificationBox>
  );
};

export default ConsultationStep3;

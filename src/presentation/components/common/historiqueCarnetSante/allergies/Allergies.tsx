import { useAllergie } from "@/presentation/hooks/carnetDeSante/sousCarnet/useAllergie";
import { Box, Checkbox, TextField, Typography } from "@mui/material";

interface AllergiesProps {
  item: string;
}

const reactions = [
  "Urticaire",
  "Toux ou respiration sifflante",
  "Rougeur ou éruption cutanée",
  "Vertiges et/ou étourdissements",
  "Sensation de picotement ou de démangeaisons dans la bouche",
  "Gonflement de la gorge et des cordes vocales",
  "Gonflement du visage, de la langue ou des lèvres",
];

const Allergies = ({ item }: AllergiesProps) => {
  const { allergieState, handleReactionsChange, handleRemarksChange } =
    useAllergie();
  return (
    <>
      <Typography variant="subtitle1" sx={{ mt: 3, mb: 1, fontWeight: "bold" }}>
        {item}
      </Typography>
      <Typography variant="subtitle2" color="textSecondary" sx={{ mb: 2 }}>
        RÉACTION (S)
      </Typography>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {reactions.map((reaction) => (
          <Box
            key={reaction}
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              gap: 1,
            }}
          >
            <Typography variant="body2">{reaction}</Typography>
            <Checkbox
              size="small"
              checked={
                allergieState.selectedReactions[item]?.[reaction] || false
              }
              onChange={(e) =>
                handleReactionsChange(item, reaction, e.target.checked)
              }
            />
          </Box>
        ))}
      </div>
      <TextField
        placeholder="Remarques"
        multiline
        rows={2}
        fullWidth
        sx={{ mt: 2 }}
        value={allergieState.remarks[item] || ""}
        onChange={(e) => handleRemarksChange(item, e.target.value)}
      />
    </>
  );
};

export default Allergies;

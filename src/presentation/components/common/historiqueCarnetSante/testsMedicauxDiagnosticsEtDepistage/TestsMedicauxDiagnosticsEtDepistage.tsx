import {
  Box,
  TextField,
  Typography,
  Autocomplete,
  Button,
  Paper,
} from "@mui/material";
import { CloudUpload } from "@mui/icons-material";
import { styled } from "@mui/material/styles";
import { useState } from "react";
import { useDiagnostic } from "@/presentation/hooks/carnetDeSante/sousCarnet/useDiagnostic";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";

const VisuallyHiddenInput = styled("input")`
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  bottom: 0;
  left: 0;
  white-space: nowrap;
  width: 1px;
`;

const options = ["Labo", "Radiographie", "IRM", "CT-Scan", "Ultrason", "Autre"];

const TestsMedicauxDiagnosticsEtDepistage = () => {
  const { selectedFile, setSelectedFile } = useCarnetDeSanteState();
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setSelectedFile(event.target.files[0]);
      console.log(event.target.files[0]);
    }
  };

  const {
    diagnosticState,
    handleTitleChange,
    handleTypeFichierChange,
    handleImpressionResultatChange,
    handleRemarksChange,
  } = useDiagnostic();

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
      <Box>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          TITRE
        </Typography>
        <TextField
          fullWidth
          size="small"
          placeholder="Entrer le titre"
          value={diagnosticState.titre}
          onChange={(e) => handleTitleChange(e.target.value)}
        />
      </Box>

      <Box>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          TYPE DE FICHIER
        </Typography>
        <Autocomplete
          size="small"
          options={options}
          value={diagnosticState.type_fichier}
          onChange={(_, newValue) => handleTypeFichierChange(newValue)}
          renderInput={(params) => (
            <TextField {...params} placeholder="Sélectionner le type" />
          )}
        />
      </Box>

      <Box>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          FICHIER (PNG, GIF, JPEG, PDF, NII, DCM)
        </Typography>
        {!diagnosticState.path || selectedFile ? (
          <Paper
            variant="outlined"
            sx={{
              textAlign: "center",
              backgroundColor: "background.default",
              cursor: "pointer",
              "&:hover": {
                backgroundColor: "action.hover",
              },
            }}
          >
            <Button
              component="label"
              variant="text"
              startIcon={<CloudUpload />}
              sx={{ width: "100%" }}
            >
              {selectedFile
                ? selectedFile.name
                : "Sélectionnez un fichier jusqu'à 5 MB"}
              <VisuallyHiddenInput type="file" onChange={handleFileChange} />
            </Button>
          </Paper>
        ) : (
          <div>
            <img src={diagnosticState.path} />
            <Button
              component="label"
              variant="text"
              startIcon={<CloudUpload />}
              sx={{ width: "100%" }}
            >
              Sélectionnez un fichier jusqu'à 5 MB
              <VisuallyHiddenInput type="file" onChange={handleFileChange} />
            </Button>
          </div>
        )}
      </Box>

      <Box>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          IMPRESSIONS/RÉSULTATS (FACULTATIF)
        </Typography>
        <TextField
          placeholder="Entrer les impressions/résultats"
          multiline
          rows={3}
          fullWidth
          size="small"
          value={diagnosticState.impression_resultat}
          onChange={(e) => handleImpressionResultatChange(e.target.value)}
        />
      </Box>

      <Box>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          REMARQUES (FACULTATIF)
        </Typography>
        <TextField
          placeholder="Entrer les remarques"
          multiline
          rows={3}
          fullWidth
          size="small"
          value={diagnosticState.remarks}
          onChange={(e) => handleRemarksChange(e.target.value)}
        />
      </Box>
    </Box>
  );
};

export default TestsMedicauxDiagnosticsEtDepistage;

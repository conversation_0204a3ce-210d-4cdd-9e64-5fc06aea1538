import { <PERSON>, Button, TextField, Typography } from "@mui/material";
import { useState } from "react";
import DatePickerModal from "@/presentation/components/common/Modal/DatePickerModal";
import { useVaccination } from "@/presentation/hooks/carnetDeSante/sousCarnet/useVaccination";

interface VaccinationProps {
  item: string;
}

const Vaccination = ({ item }: VaccinationProps) => {
  const [datePickerType, setDatePickerType] = useState<
    "administration" | "prochaine" | null
  >(null);
  const onDateAdministrationChange = (value: Date | null) => {
    if (!value) return;
    handleDateAdministrationChange(item, value);
  };
  const onProchaineDateChange = (value: Date | null) => {
    if (!value) return;
    handleProchaineDateChange(item, value);
  };
  const {
    vaccinationState,
    handleDateAdministrationChange,
    handleProchaineDateChange,
    handleRemarksChange,
  } = useVaccination();
  return (
    <>
      <Typography variant="subtitle1" sx={{ mt: 3, mb: 1, fontWeight: "bold" }}>
        {item}
      </Typography>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-3">
        <div>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            Date d'administration
          </Typography>
          <Box className="flex items-center gap-2 my-2 h-10">
            <Button
              variant="outlined"
              onClick={() => setDatePickerType("administration")}
              sx={{ textTransform: "none" }}
            >
              {vaccinationState.dateAdministration[item]
                ? new Date(
                    vaccinationState.dateAdministration[item]
                  ).toLocaleDateString()
                : "Sélectionner une date"}
            </Button>
          </Box>
        </div>
        <div>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ mb: 1, display: "block" }}
          >
            Prochaine date d'écheance
          </Typography>
          <Box className="flex items-center gap-2 my-2 h-10">
            <Button
              variant="outlined"
              onClick={() => setDatePickerType("prochaine")}
              sx={{ textTransform: "none" }}
            >
              {vaccinationState.prochaineDate[item]
                ? new Date(
                    vaccinationState.prochaineDate[item]
                  ).toLocaleDateString()
                : "Sélectionner une date"}
            </Button>
          </Box>
        </div>
      </div>
      <TextField
        placeholder="Remarques"
        multiline
        rows={2}
        fullWidth
        sx={{ mt: 2 }}
        value={vaccinationState.remarks[item] || ""}
        onChange={(e) => handleRemarksChange(item, e.target.value)}
      />
      {datePickerType && (
        <DatePickerModal
          open={!!datePickerType}
          onClose={() => setDatePickerType(null)}
          onDateSelect={
            datePickerType === "administration"
              ? onDateAdministrationChange
              : onProchaineDateChange
          }
        />
      )}
    </>
  );
};

export default Vaccination;

import useLocationFilter from "@/presentation/hooks/use-location-filter";
import { Autocomplete, TextField, InputAdornment, Box } from "@mui/material";
import { MapPin } from "lucide-react";
import React, { SetStateAction, useState, useCallback } from "react";
import useDebouncedCallback from "@/presentation/hooks/use-debounced-callback";
import { highlightText } from "@/shared/utils/highlightText";
import { motion } from "framer-motion";
import { styled } from "@mui/material/styles";
import { SECONDARY,GREEN_2 ,PRIMARY,BLEU_FONCE} from "@/shared/constants/Color";

// Style pour l'icône avec dégradé vert
const GradientIconWrapper = styled('div')({
  background: 'linear-gradient(135deg, '+PRIMARY+', '+SECONDARY+')',
  borderRadius: '50%',
  padding: '6px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  boxShadow: '0 2px 8px rgba(76, 175, 80, 0.3)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'scale(1.1)',
    boxShadow: '0 4px 12px rgba(76, 175, 80, 0.4)',
  }
});

type LocationOption = {
  id: string;
  label: string;
  type: 'district' | 'commune' | 'location';
};

const LOCATIONS: LocationOption[] = [
  "Antananarivo",
  "Antsiranana",
  "Fianarantsoa",
  "Mahajanga",
  "Toamasina",
  "Toliara",
].map(location => ({
  id: `location-${location}`,
  label: location,
  type: 'location' as const
}));

export const LocationSearchBar = ({
  inputValue,
  setInputValue,
}: {
  inputValue: string;
  setInputValue: React.Dispatch<SetStateAction<string>>;
}) => {
  const [locations, setLocations] = useState<LocationOption[]>(LOCATIONS);
  const { filteredCommunes, filteredDistricts, search } = useLocationFilter();
  // État pour suivre si l'utilisateur est en train de taper
  const [isTyping, setIsTyping] = useState(false);

  const handleSearch = useCallback(async (value: string) => {
    if (!value) return;
    await search(value);
    const districtsOptions = filteredDistricts
      ? filteredDistricts.map(district => ({
        id: `district-${district.id}`,
        label: district.libelle,
        type: 'district' as const
      }))
      : [];
    const communesOptions = filteredCommunes
      ? filteredCommunes.map(commune => ({
        id: `commune-${commune.id}`,
        label: commune.nom,
        type: 'commune' as const
      }))
      : [];
    setLocations([...districtsOptions, ...communesOptions, ...LOCATIONS]);
  }, [filteredDistricts, filteredCommunes, search]);

  const debouncedSearch = useDebouncedCallback(handleSearch, 300);

  const handleChange = (newValue: string) => {
    setInputValue(newValue || "");
    debouncedSearch(newValue);

    // Activer l'animation de rotation quand l'utilisateur tape
    if (newValue !== inputValue) {
      setIsTyping(true);
      // Désactiver l'animation après un délai
      setTimeout(() => setIsTyping(false), 1000);
    }
  };

  return (
    <Box className="w-full ml-1">

    <Autocomplete
      value={locations.find(loc => loc.label === inputValue) || null}
      inputValue={inputValue}
      onInputChange={(_, newValue) => handleChange(newValue)}
      onChange={(_, selected) => handleChange(selected?.label || '')}
      options={locations || []}
      getOptionLabel={(option) => option.label}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      renderOption={(props, option) => {
        const { key, ...otherProps } = props;
        return (
          <li {...otherProps} key={key}>
            {highlightText(option.label, inputValue)}
          </li>
        );
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder="Où? (ville, code postal, adresse)"
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <InputAdornment position="start">
                <GradientIconWrapper>
                  <motion.div
                    animate={{
                      rotate: isTyping ? 360 : 0,
                      scale: isTyping ? 1.2 : 1
                    }}
                    transition={{
                      type: "spring",
                      stiffness: 260,
                      damping: 20,
                      duration: 0.8
                    }}
                  >
                    <MapPin className="h-4 w-4 text-white" />
                  </motion.div>
                </GradientIconWrapper>
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              "& fieldset": {
                borderColor: "transparent",
              },
              "&:hover fieldset": {
                borderColor: "transparent",
              },
              "&.Mui-focused fieldset": {
                borderColor: "transparent",
              },
            },
          }}
        />
      )}
      sx={{
        width: "100%",
        "& .MuiAutocomplete-input": {
        },
        "& .MuiInputBase-input::placeholder": {
          color: "gray",
          fontSize: "15px",
          opacity: 1,
        },
      }}
    />
    </Box>
  );
};

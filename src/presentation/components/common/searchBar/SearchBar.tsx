import { Search } from "lucide-react";
import { ComponentProps, useState, useRef, useEffect } from "react";
import { twMerge } from "tailwind-merge";
import { LocationSearchBar } from "./LocationSearchBar";
import { SpecialitiesSearchBar } from "./SpecialitiesSearchBar";
import Button from "../Button/Button";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";

import { Box, Paper } from "@mui/material";
import SpecialitiesFilter from "./filters/SpecialitiesFilter";
import DoctorsFilter from "./filters/DoctorsFilter";
import EtablishmentsFilter from "./filters/EtablishmentsFilter";

interface searchBarUrlParams {
  searchname: string;
  searchlocation: string;
}

const SearchBar = ({ className, ...props }: ComponentProps<"div">) => {
  const [isOpen, setIsOpen] = useState(false);
  const searchBarRef = useRef<HTMLDivElement | null>(null);
  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchBarRef.current &&
        !searchBarRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  const {
    searchNameKey,
    setSearchNameKey,
    searchLocationKey,
    setSearchLocationKey,
    searchProfessional,
  } = useSearchProfessional();

  const navigate = useNavigate();
  const location = useLocation();

  const updateUrlParams = () => {
    setSearchParams({
      searchname: searchNameKey || "",
      searchlocation: searchLocationKey || "",
    });
  };

  const handleSearch = async () => {
    if (searchNameKey === "" && searchLocationKey === "") return;
    const urlParams: searchBarUrlParams = {
      searchname: "searchname",
      searchlocation: "searchlocation",
    };

    if (
      location.pathname.includes(PublicRoutesNavigation.FIND_PROFESSIONAL) &&
      searchParams &&
      (searchParams.get(urlParams.searchname) !== searchNameKey ||
        searchParams.get(urlParams.searchlocation) !== searchLocationKey)
    ) {
      await searchProfessional({
        name: searchNameKey || "",
        localization: searchLocationKey || "",
      });

      updateUrlParams();
    } else {
      navigate(
        `/${PublicRoutesNavigation.FIND_PROFESSIONAL}?${urlParams.searchname}=${encodeURIComponent(searchNameKey || "")}&${urlParams.searchlocation}=${encodeURIComponent(searchLocationKey || "")}`,
      );
    }
  };

  const handleSelectSearchTerm = (searchTerm: string) => {
    setSearchNameKey(searchTerm);
    setIsOpen(false);
  };

  const handleClickProfessionnal = (professionalId: number) => {
    navigate(`/profile/${professionalId}`);
  };

  const handleSubmitForm = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    handleSearch();
  };

  return (
    <div
      ref={searchBarRef}
      className={twMerge(
        "mx-auto space-y-4 border border-meddoc-secondary rounded-md z-999",
        className,
      )}
      {...props}
    >
      {/* Search Inputs */}
      <form
        onSubmit={(e) => handleSubmitForm(e)}
        className="flex flex-col md:flex-row items-center gap-4 bg-white relative w-full rounded-md "
      >
        <div className="relative flex-1 w-full md:w-auto max-h-[300px]">
          <SpecialitiesSearchBar
            value={searchNameKey || ""}
            setValue={setSearchNameKey}
            isCompletionOpen={isOpen}
            setIsCompletionOpen={setIsOpen}
          />
        </div>
        {/* <div className="hidden md:flex w-1 h-[50%] bg-meddoc-secondary">

        </div> */}
        <div className="relative flex-1 w-full md:w-auto">
          <LocationSearchBar
            inputValue={searchLocationKey || ""}
            setInputValue={setSearchLocationKey}
          />
        </div>

        <Button onClick={() => handleSearch()} className="w-full md:w-auto md:m-1 bg-gradient-to-r  from-meddoc-primary to-meddoc-secondary hover:from-meddoc-fonce/90 hover:to-meddoc-primary/90 text-white font-semibold p-2  rounded-md shadow-md transition-all duration-200">
          <Search className="m-2 h-5 w-5" /> Rechercher
        </Button>
        {/* Autocomplete */}
        {isOpen && (
          <Paper className="absolute mt-4 rounded-md overflow-hidden w-full top-12 z-50">
            <Paper className="p-3">
              <Box className="flex flex-col md:flex-row gap-2 w-full bg-white">
                {/* Spécialités Section */}
                <SpecialitiesFilter
                  searchTerm={searchNameKey || ""}
                  handleOptionSelect={handleSelectSearchTerm}
                />

                {/* Praticiens Section */}
                <DoctorsFilter
                  isOpen={isOpen}
                  searchTerm={searchNameKey || ""}
                  handleOptionSelect={handleClickProfessionnal}
                />

                {/* Établissements Section */}
                <EtablishmentsFilter
                  isOpen={isOpen}
                  searchTerm={searchNameKey || ""}
                  handleOptionSelect={handleClickProfessionnal}
                />
              </Box>
            </Paper>
          </Paper>
        )}
      </form>
    </div>
  );
};

export default SearchBar;

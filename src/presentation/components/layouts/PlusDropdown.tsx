import * as React from "react";
import { useAgendaState } from "@/presentation/hooks/agenda";
import { Typography } from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import Stack from "@mui/material/Stack";
import Tooltip from "@mui/material/Tooltip";
import {
  CalendarCheck2,
  CalendarRangeIcon,
  PlusIcon,
  Settings2Icon,
} from "lucide-react";

// Composant pour l'icône utilisateur
export const PlusDropdown = () => {
  const {
    handleIsSettingsModalOpen,
    handleIsAddEventModalOpen,
    handleIsAppointmentModalOpen,
  } = useAgendaState();

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleAddEventClick = () => {
    handleIsAddEventModalOpen(true);
    handleClose();
  };

  const handleSettingsClick = () => {
    handleIsSettingsModalOpen(true);
    handleClose();
  };

  const handleAppointmentClick = () => {
    handleIsAppointmentModalOpen(true);
    handleClose();
  };

  return (
    <Stack direction="row" alignItems="center">
      <Tooltip title="Creer" enterDelay={1000}>
        <IconButton
          onClick={handleClick}
          color="inherit"
          aria-label="user menu"
          size="large"
        >
          <PlusIcon fontSize="large" />
        </IconButton>
      </Tooltip>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem
          onClick={handleAddEventClick}
          className="flex items-center gap-2"
        >
          <CalendarRangeIcon />
          <Typography>Evenements</Typography>
        </MenuItem>
        <MenuItem
          onClick={handleSettingsClick}
          className="flex items-center gap-2"
        >
          <Settings2Icon />
          <Typography>Planning de rendez-vous</Typography>
        </MenuItem>
        <MenuItem
          onClick={handleAppointmentClick}
          className="flex items-center gap-2"
        >
          <CalendarCheck2 />
          <Typography>Reserver un rendez-vous</Typography>
        </MenuItem>
      </Menu>
    </Stack>
  );
};
